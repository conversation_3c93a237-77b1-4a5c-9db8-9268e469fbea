{"version": 3, "file": "watchBlocks.js", "sourceRoot": "", "sources": ["../../../actions/public/watchBlocks.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAA;AAChD,OAAO,EAAsB,IAAI,EAAE,MAAM,qBAAqB,CAAA;AAC9D,OAAO,EAA2B,SAAS,EAAE,MAAM,0BAA0B,CAAA;AAE7E,OAAO,EAA2B,QAAQ,EAAE,MAAM,eAAe,CAAA;AA6DjE;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,WAAW,CAMzB,MAAgC,EAChC,EACE,QAAQ,GAAG,QAAQ,EACnB,UAAU,GAAG,KAAK,EAClB,WAAW,GAAG,KAAK,EACnB,OAAO,EACP,OAAO,EACP,mBAAmB,EAAE,oBAAoB,EACzC,IAAI,EAAE,KAAK,EACX,eAAe,GAAG,MAAM,CAAC,eAAe,GAC+B;IAEzE,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,WAAW;YAAE,OAAO,KAAK,CAAA;QAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW;YAAE,OAAO,KAAK,CAAA;QACvD,IACE,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU;YACpC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW;YAE1D,OAAO,KAAK,CAAA;QACd,OAAO,IAAI,CAAA;IACb,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,mBAAmB,GAAG,oBAAoB,IAAI,KAAK,CAAA;IAEzD,IAAI,SAES,CAAA;IAEb,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,MAAM,UAAU,GAAG,SAAS,CAAC;YAC3B,aAAa;YACb,MAAM,CAAC,GAAG;YACV,QAAQ;YACR,UAAU;YACV,WAAW;YACX,mBAAmB;YACnB,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CACxD,IAAI,CACF,KAAK,IAAI,EAAE;YACT,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,SAAS,CAC3B,MAAM,EACN,QAAQ,EACR,UAAU,CACX,CAAC;oBACA,QAAQ;oBACR,mBAAmB;iBACpB,CAAC,CAAA;gBACF,IAAI,KAAK,CAAC,MAAM,IAAI,SAAS,EAAE,MAAM,EAAE,CAAC;oBACtC,2DAA2D;oBAC3D,eAAe;oBACf,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;wBAAE,OAAM;oBAE7C,yDAAyD;oBACzD,wDAAwD;oBACxD,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;wBACtD,KAAK,IAAI,CAAC,GAAG,SAAS,EAAE,MAAM,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC3D,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAC5B,MAAM,EACN,QAAQ,EACR,UAAU,CACX,CAAC;gCACA,WAAW,EAAE,CAAC;gCACd,mBAAmB;6BACpB,CAAC,CAA8B,CAAA;4BAChC,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;4BAC5C,SAAS,GAAG,KAAK,CAAA;wBACnB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED;gBACE,qCAAqC;gBACrC,CAAC,SAAS,EAAE,MAAM;oBAClB,4DAA4D;oBAC5D,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC;oBAC1C,4EAA4E;oBAC5E,4CAA4C;oBAC5C,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,EACjD,CAAC;oBACD,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;oBAC5C,SAAS,GAAG,KAAY,CAAA;gBAC1B,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,EACD;YACE,WAAW;YACX,QAAQ,EAAE,eAAe;SAC1B,CACF,CACF,CAAA;IACH,CAAC,CAAA;IAED,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAA;QACjB,IAAI,WAAW,GAAG,IAAI,CAAA;QACtB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CACvC;QAAA,CAAC,KAAK,IAAI,EAAE;YACX,IAAI,CAAC;gBACH,IAAI,WAAW,EAAE,CAAC;oBAChB,SAAS,CACP,MAAM,EACN,QAAQ,EACR,UAAU,CACX,CAAC;wBACA,QAAQ;wBACR,mBAAmB;qBACpB,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBAChB,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,IAAI,CAAC,WAAW;4BAAE,OAAM;wBACxB,OAAO,CAAC,KAAY,EAAE,SAAS,CAAC,CAAA;wBAChC,WAAW,GAAG,KAAK,CAAA;oBACrB,CAAC,CAAC,CAAA;gBACJ,CAAC;gBAED,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;oBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAChD,CAAC,SAAgC,EAAE,EAAE,CACnC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CACxC,CAAA;wBACD,IAAI,CAAC,SAAS;4BAAE,OAAO,MAAM,CAAC,SAAS,CAAA;wBACvC,OAAO,SAAS,CAAC,KAAK,CAAA;oBACxB,CAAC;oBACD,OAAO,MAAM,CAAC,SAAS,CAAA;gBACzB,CAAC,CAAC,EAAE,CAAA;gBAEJ,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC;oBAC9D,MAAM,EAAE,CAAC,UAAU,CAAC;oBACpB,KAAK,CAAC,MAAM,CAAC,IAAS;wBACpB,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAC5B,MAAM,EACN,QAAQ,EACR,UAAU,CACX,CAAC;4BACA,WAAW,EAAE,IAAI,CAAC,WAAW;4BAC7B,mBAAmB;yBACpB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAA8B,CAAA;wBAChD,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;wBACvC,WAAW,GAAG,KAAK,CAAA;wBACnB,SAAS,GAAG,KAAK,CAAA;oBACnB,CAAC;oBACD,OAAO,CAAC,KAAY;wBAClB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;oBAClB,CAAC;iBACF,CAAC,CAAA;gBACF,WAAW,GAAG,YAAY,CAAA;gBAC1B,IAAI,CAAC,MAAM;oBAAE,WAAW,EAAE,CAAA;YAC5B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,EAAE,CAAA;QACJ,OAAO,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;IAC5B,CAAC,CAAA;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAA;AACzD,CAAC"}