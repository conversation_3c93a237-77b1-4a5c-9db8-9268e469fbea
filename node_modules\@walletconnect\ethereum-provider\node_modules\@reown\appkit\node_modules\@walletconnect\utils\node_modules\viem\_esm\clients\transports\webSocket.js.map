{"version": 3, "file": "webSocket.js", "sourceRoot": "", "sources": ["../../../clients/transports/webSocket.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AACzD,OAAO,EACL,gBAAgB,GAEjB,MAAM,2BAA2B,CAAA;AAIlC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAA;AAErD,OAAO,EAEL,qBAAqB,GACtB,MAAM,8BAA8B,CAAA;AACrC,OAAO,EAIL,eAAe,GAChB,MAAM,sBAAsB,CAAA;AAkE7B;;GAEG;AACH,MAAM,UAAU,SAAS;AACvB,uEAAuE;AACvE,GAAY,EACZ,SAAmC,EAAE;IAErC,MAAM,EACJ,SAAS,EACT,GAAG,GAAG,WAAW,EACjB,OAAO,EACP,IAAI,GAAG,oBAAoB,EAC3B,SAAS,EACT,UAAU,GACX,GAAG,MAAM,CAAA;IACV,OAAO,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;QAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,WAAW,CAAA;QACnD,MAAM,OAAO,GAAG,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAA;QACpD,MAAM,IAAI,GAAG,GAAG,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAA;QACzD,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,gBAAgB,EAAE,CAAA;QACvC,OAAO,eAAe,CACpB;YACE,GAAG;YACH,OAAO;YACP,IAAI;YACJ,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE;gBAC9B,MAAM,IAAI,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;gBAC/B,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,IAAI,EAAE;oBAClD,SAAS;oBACT,SAAS;iBACV,CAAC,CAAA;gBACF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC;oBACrD,IAAI;oBACJ,OAAO;iBACR,CAAC,CAAA;gBACF,IAAI,KAAK;oBACP,MAAM,IAAI,eAAe,CAAC;wBACxB,IAAI;wBACJ,KAAK;wBACL,GAAG,EAAE,IAAI;qBACV,CAAC,CAAA;gBACJ,OAAO,MAAM,CAAA;YACf,CAAC;YACD,UAAU;YACV,UAAU;YACV,OAAO;YACP,IAAI,EAAE,WAAW;SAClB,EACD;YACE,SAAS;gBACP,OAAO,SAAS,CAAC,IAAI,CAAC,CAAA;YACxB,CAAC;YACD,YAAY;gBACV,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAA;YACpC,CAAC;YACD,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAO;gBAC9C,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,IAAI,CAAC,CAAA;gBACnD,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,IAAI,OAAO,CAClD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAClB,SAAS,CAAC,OAAO,CAAC;oBAChB,IAAI,EAAE;wBACJ,MAAM,EAAE,eAAe;wBACvB,MAAM;qBACP;oBACD,OAAO,CAAC,KAAK;wBACX,MAAM,CAAC,KAAK,CAAC,CAAA;wBACb,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;wBAChB,OAAM;oBACR,CAAC;oBACD,UAAU,CAAC,QAAQ;wBACjB,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;4BACnB,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;4BACtB,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;4BACzB,OAAM;wBACR,CAAC;wBAED,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC;4BACpC,OAAO,CAAC,QAAQ,CAAC,CAAA;4BACjB,OAAM;wBACR,CAAC;wBACD,IAAI,QAAQ,CAAC,MAAM,KAAK,kBAAkB;4BAAE,OAAM;wBAClD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;oBACzB,CAAC;iBACF,CAAC,CACL,CAAA;gBACD,OAAO;oBACL,cAAc;oBACd,KAAK,CAAC,WAAW;wBACf,OAAO,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,EAAE,CAClC,SAAS,CAAC,OAAO,CAAC;4BAChB,IAAI,EAAE;gCACJ,MAAM,EAAE,iBAAiB;gCACzB,MAAM,EAAE,CAAC,cAAc,CAAC;6BACzB;4BACD,UAAU,EAAE,OAAO;yBACpB,CAAC,CACH,CAAA;oBACH,CAAC;iBACF,CAAA;YACH,CAAC;SACF,CACF,CAAA;IACH,CAAC,CAAA;AACH,CAAC"}