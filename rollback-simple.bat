@echo off
echo ========================================
echo XMKF数字资产平台 - 简化版回滚脚本
echo ========================================
echo.

echo 警告: 此操作将回滚到原始版本，Reown AppKit功能将被移除
echo.
set /p confirm="确定要继续回滚吗? (y/n): "
if /i not "%confirm%"=="y" (
    echo 回滚操作已取消
    pause
    exit /b 0
)

echo.
echo 开始回滚操作...

:: 检查备份文件是否存在
if not exist "public\hilltop\usdc\trade\index\usdc-original-backup.html" (
    echo 错误: 未找到备份文件 usdc-original-backup.html
    echo 无法执行回滚操作
    echo.
    echo 可能的解决方案:
    echo 1. 检查是否在正确的目录中运行脚本
    echo 2. 确认备份文件是否存在
    echo 3. 手动恢复原始文件
    pause
    exit /b 1
)

:: 创建当前版本的备份
echo 1. 备份当前版本...
if not exist "backup\rollback" mkdir "backup\rollback"
if exist "public\hilltop\usdc\trade\index\usdc.html" (
    copy "public\hilltop\usdc\trade\index\usdc.html" "backup\rollback\usdc-reown.html.bak"
    echo ✓ 当前版本已备份到 backup\rollback\usdc-reown.html.bak
)

:: 恢复原始文件
echo 2. 恢复原始文件...
copy "public\hilltop\usdc\trade\index\usdc-original-backup.html" "public\hilltop\usdc\trade\index\usdc.html"
if %errorlevel% neq 0 (
    echo ❌ 文件恢复失败
    echo 请手动将 usdc-original-backup.html 重命名为 usdc.html
    pause
    exit /b 1
)
echo ✓ usdc.html 已恢复到原始版本

:: 询问是否删除新增文件
echo 3. 清理新增文件...
set /p cleanup="是否删除新增的Reown AppKit相关文件? (y/n): "
if /i "%cleanup%"=="y" (
    if exist "public\hilltop\usdc\trade\index\js\wallet-manager-simple.js" (
        del "public\hilltop\usdc\trade\index\js\wallet-manager-simple.js"
        echo ✓ 已删除 wallet-manager-simple.js
    )
    
    if exist "deploy-simple.bat" (
        echo 保留 deploy-simple.bat (可手动删除)
    )
    
    if exist "rollback-simple.bat" (
        echo 保留 rollback-simple.bat (当前脚本)
    )
    
    echo ✓ 新增文件清理完成
) else (
    echo ℹ️  保留新增文件，可手动删除:
    echo   - public\hilltop\usdc\trade\index\js\wallet-manager-simple.js
    echo   - deploy-simple.bat
    echo   - rollback-simple.bat
)

:: 验证回滚结果
echo 4. 验证回滚结果...

if exist "public\hilltop\usdc\trade\index\usdc.html" (
    echo ✓ usdc.html 文件存在
) else (
    echo ❌ usdc.html 文件缺失
    pause
    exit /b 1
)

:: 检查是否还包含Reown AppKit引用
findstr /C:"@reown/appkit" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo ⚠️  警告: 文件中仍包含Reown AppKit引用，回滚可能不完整
) else (
    echo ✓ Reown AppKit引用已移除
)

:: 检查是否还包含钱包管理器引用
findstr /C:"wallet-manager-simple.js" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo ⚠️  警告: 文件中仍包含钱包管理器引用，回滚可能不完整
) else (
    echo ✓ 钱包管理器引用已移除
)

echo.
echo ========================================
echo 回滚完成！
echo ========================================
echo.
echo 已执行的操作:
echo - ✓ 恢复原始 usdc.html 文件
echo - ✓ 当前版本备份到 backup\rollback\
if /i "%cleanup%"=="y" (
    echo - ✓ 清理了新增的Reown AppKit文件
) else (
    echo - ℹ️  保留了新增的Reown AppKit文件
)
echo.
echo 系统已回滚到使用Web3Modal的原始版本
echo.
echo 原始功能:
echo - Web3Modal钱包连接
echo - MetaMask和WalletConnect支持
echo - 原有的交易授权逻辑
echo - 所有原始UI和功能
echo.

:: 询问是否立即测试
set /p test_now="是否立即在浏览器中打开测试页面验证回滚? (y/n): "
if /i "%test_now%"=="y" (
    echo 正在打开测试页面...
    start "" "http://localhost/hilltop/usdc/trade/index/usdc.html"
    echo.
    echo 请验证以下功能是否正常:
    echo 1. 页面正常加载
    echo 2. 原有的钱包连接功能
    echo 3. 交易授权功能
    echo 4. 所有原始UI元素
)

echo.
echo ========================================
echo 重要提醒
echo ========================================
echo.
echo 如需重新部署Reown AppKit:
echo 1. 运行 deploy-simple.bat
echo 2. 或者手动恢复修改的文件
echo.
echo 备份文件位置:
echo - 原始版本: usdc-original-backup.html
echo - Reown版本: backup\rollback\usdc-reown.html.bak
echo.
echo 如果遇到问题:
echo 1. 检查文件权限
echo 2. 确认备份文件完整性
echo 3. 手动复制文件进行恢复
echo.

echo 回滚脚本执行完成！
pause
