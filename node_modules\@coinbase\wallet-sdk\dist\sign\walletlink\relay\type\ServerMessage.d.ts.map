{"version": 3, "file": "ServerMessage.d.ts", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/type/ServerMessage.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAEhD,MAAM,MAAM,aAAa,CAAC,CAAC,SAAS,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC,cAAc,EAAE;IAAE,IAAI,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC;AAExF,MAAM,MAAM,iBAAiB,GAAG,IAAI,CAAC;AACrC,KAAK,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;AAEnC,KAAK,cAAc,GACf;IACE,IAAI,EAAE,WAAW,CAAC;CACnB,GACD;IACE,IAAI,EAAE,IAAI,CAAC;IACX,EAAE,EAAE,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACnB,GACD;IACE,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;CACf,GACD;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,EAAE,EAAE,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,OAAO,CAAC;IAChB,YAAY,EAAE,MAAM,CAAC;CACtB,GACD;IACE,IAAI,EAAE,QAAQ,CAAC;IACf,EAAE,CAAC,EAAE,SAAS,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;CACtB,GACD;IACE,IAAI,EAAE,oBAAoB,CAAC;IAC3B,EAAE,EAAE,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE;QAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACvC,GACD;IACE,IAAI,EAAE,sBAAsB,CAAC;IAC7B,EAAE,CAAC,EAAE,SAAS,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE;QAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACvC,GACD;IACE,IAAI,EAAE,gBAAgB,CAAC;IACvB,EAAE,EAAE,SAAS,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,OAAO,CAAC;IACd,EAAE,CAAC,EAAE,SAAS,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;CACd,CAAC"}