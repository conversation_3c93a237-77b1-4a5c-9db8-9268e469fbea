{"version": 3, "file": "waitToProve.js", "sourceRoot": "", "sources": ["../../../op-stack/actions/waitToProve.ts"], "names": [], "mappings": "AAWA,OAAO,EAAE,iCAAiC,EAAE,MAAM,yBAAyB,CAAA;AAG3E,OAAO,EAEL,cAAc,GACf,MAAM,4BAA4B,CAAA;AACnC,OAAO,EAEL,gBAAgB,GACjB,MAAM,uBAAuB,CAAA;AAC9B,OAAO,EAGL,eAAe,GAChB,MAAM,sBAAsB,CAAA;AAC7B,OAAO,EAIL,mBAAmB,GACpB,MAAM,0BAA0B,CAAA;AAqCjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAK/B,MAAyC,EACzC,UAAuD;IAEvD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,UAAU,CAAA;IAEzC,MAAM,CAAC,UAAU,CAAC,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;IAE5C,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,iCAAiC,CAAC;YAC1C,IAAI,EAAE,OAAO,CAAC,eAAe;SAC9B,CAAC,CAAA;IAEJ,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAC1C,MAAM,EACN,UAAwC,CACzC,CAAA;IAED,uBAAuB;IACvB,IAAI,aAAa,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE;YAC/C,GAAG,UAAU;YACb,aAAa,EAAE,OAAO,CAAC,WAAW;SACF,CAAC,CAAA;QACnC,OAAO;YACL,IAAI,EAAE;gBACJ,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,MAAM,CAAC,WAAW;gBACzB,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,MAAM,CAAC,UAAU;gBAC5B,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B;YACD,MAAM;YACN,UAAU;SACX,CAAA;IACH,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,MAAM,EAAE;QACzC,GAAG,UAAU;QACb,KAAK,EAAE,SAAS;QAChB,aAAa,EAAE,OAAO,CAAC,WAAW;KACN,CAAC,CAAA;IAC/B,OAAO;QACL,IAAI;QACJ,MAAM,EAAE;YACN,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,KAAK;YACvB,UAAU,EAAE,IAAI,CAAC,SAAS;YAC1B,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B;QACD,UAAU;KACX,CAAA;AACH,CAAC"}