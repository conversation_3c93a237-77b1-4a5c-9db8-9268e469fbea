{"version": 3, "file": "WalletLinkHTTP.js", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/connection/WalletLinkHTTP.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,cAAc;IAGzB,YACmB,UAAkB,EAClB,SAAiB,EAClC,UAAkB;QAFD,eAAU,GAAV,UAAU,CAAQ;QAClB,cAAS,GAAT,SAAS,CAAQ;QAGlC,MAAM,WAAW,GAAG,GAAG,SAAS,IAAI,UAAU,EAAE,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;IAC3C,CAAC;IAED,6BAA6B;IACrB,KAAK,CAAC,sBAAsB,CAAC,MAAgC;QACnE,OAAO,OAAO,CAAC,GAAG,CAChB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACf,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,WAAW,CAAC,CAAC,OAAO,OAAO,EAAE;YACnD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,aAAa,EAAE,IAAI,CAAC,IAAI;aACzB;SACF,CAAC,CACH,CACF,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED,KAAK,CAAC,iBAAiB;;QACrB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,qBAAqB,EAAE;YACpE,OAAO,EAAE;gBACP,aAAa,EAAE,IAAI,CAAC,IAAI;aACzB;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;YAChB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAQ/C,CAAC;YAEF,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,cAAc,GAClB,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,cAAc,EACzC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACX,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,OAAO,EAAE,CAAC,CAAC,EAAE;gBACb,KAAK,EAAE,CAAC,CAAC,KAAK;gBACd,IAAI,EAAE,CAAC,CAAC,IAAI;aACb,CAAC,CAAC,mCAAI,EAAE,CAAC;YAEd,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YAE5C,OAAO,cAAc,CAAC;QACxB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IACpE,CAAC;CACF"}