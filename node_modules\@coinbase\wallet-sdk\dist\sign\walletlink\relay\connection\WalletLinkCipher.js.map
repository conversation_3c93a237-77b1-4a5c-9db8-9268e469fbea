{"version": 3, "file": "WalletLinkCipher.js", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/connection/WalletLinkCipher.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,EAAE,qBAAqB,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAE5E,MAAM,OAAO,gBAAgB;IAC3B,qDAAqD;IACrD,YAA6B,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAAG,CAAC;IAE/C;;;;;;OAMG;IACH,KAAK,CAAC,OAAO,CAAC,SAAiB;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE;YAAE,MAAM,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,MAAM,SAAS,GAAc,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CACxD,KAAK,EACL,qBAAqB,CAAC,MAAM,CAAC,EAC7B,EAAE,IAAI,EAAE,SAAS,EAAE,EACnB,KAAK,EACL,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB,CAAC;QAEF,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;QAE9B,yFAAyF;QACzF,MAAM,eAAe,GAAgB,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CACrE;YACE,IAAI,EAAE,SAAS;YACf,EAAE,EAAE,OAAO;SACZ,EACD,SAAS,EACT,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CACtB,CAAC;QAEF,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,OAAO,GAAgB,eAAe,CAAC,KAAK,CAAC,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC;QAC3F,MAAM,kBAAkB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC;QAE5F,MAAM,YAAY,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,uBAAuB,GAAG,IAAI,UAAU,CAAC,kBAAkB,CAAC,CAAC;QACnE,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,YAAY,EAAE,GAAG,uBAAuB,CAAC,CAAC,CAAC;QAC5F,OAAO,eAAe,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CAAC,UAAkB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE;YAAE,MAAM,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACjE,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,KAAK,CAAC,KAAK;gBACT,MAAM,SAAS,GAAc,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CACxD,KAAK,EACL,qBAAqB,CAAC,MAAM,CAAC,EAC7B,EAAE,IAAI,EAAE,SAAS,EAAE,EACnB,KAAK,EACL,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB,CAAC;gBAEF,MAAM,SAAS,GAAe,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAEhE,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC7C,MAAM,uBAAuB,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACpD,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,uBAAuB,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;gBACrF,MAAM,IAAI,GAAG;oBACX,IAAI,EAAE,SAAS;oBACf,EAAE,EAAE,IAAI,UAAU,CAAC,OAAO,CAAC;iBAC5B,CAAC;gBACF,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;oBACtF,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;oBAClC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;gBACrC,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}