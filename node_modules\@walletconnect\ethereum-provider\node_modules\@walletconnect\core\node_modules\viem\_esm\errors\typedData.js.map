{"version": 3, "file": "typedData.js", "sourceRoot": "", "sources": ["../../errors/typedData.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAKrC,MAAM,OAAO,kBAAmB,SAAQ,SAAS;IAC/C,YAAY,EAAE,MAAM,EAAuB;QACzC,KAAK,CAAC,mBAAmB,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE;YAC9C,YAAY,EAAE,CAAC,iCAAiC,CAAC;SAClD,CAAC,CAAA;IACJ,CAAC;CACF;AAKD,MAAM,OAAO,uBAAwB,SAAQ,SAAS;IACpD,YAAY,EACV,WAAW,EACX,KAAK,GAC+D;QACpE,KAAK,CACH,0BAA0B,WAAW,uBAAuB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EACnG;YACE,QAAQ,EAAE,uDAAuD;YACjE,YAAY,EAAE,CAAC,kDAAkD,CAAC;SACnE,CACF,CAAA;IACH,CAAC;CACF;AAKD,MAAM,OAAO,sBAAuB,SAAQ,SAAS;IACnD,YAAY,EAAE,IAAI,EAAoB;QACpC,KAAK,CAAC,gBAAgB,IAAI,eAAe,EAAE;YACzC,YAAY,EAAE,CAAC,0CAA0C,CAAC;YAC1D,IAAI,EAAE,wBAAwB;SAC/B,CAAC,CAAA;IACJ,CAAC;CACF"}