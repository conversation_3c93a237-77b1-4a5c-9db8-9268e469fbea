{"version": 3, "file": "WalletLinkConnection.d.ts", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/connection/WalletLinkConnection.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AACrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AASvD,MAAM,WAAW,kCAAkC;IACjD,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,CAAC;IACzC,yBAAyB,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,KAAK,IAAI,CAAC;IACxE,YAAY,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,KAAK,IAAI,CAAC;IAC5D,cAAc,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK,IAAI,CAAC;IAClD,eAAe,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,KAAK,IAAI,CAAC;IAC9D,cAAc,EAAE,MAAM,IAAI,CAAC;CAC5B;AAED,UAAU,0BAA0B;IAClC,OAAO,EAAE,iBAAiB,CAAC;IAC3B,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,kCAAkC,CAAC;CAC9C;AAED;;GAEG;AACH,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,qBAAqB,CAAK;IAClC,OAAO,CAAC,SAAS,CAAgB;IAEjC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAoB;IAE5C,OAAO,CAAC,QAAQ,CAAC,CAAqC;IACtD,OAAO,CAAC,MAAM,CAAmB;IACjC,OAAO,CAAC,EAAE,CAAsB;IAChC,OAAO,CAAC,IAAI,CAAiB;IAC7B,OAAO,CAAC,eAAe,CAAC,CAAS;IAEjC;;;;;;OAMG;gBACS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,0BAA0B;IA8FzE;;OAEG;IACI,OAAO,IAAI,IAAI;IAOtB;;;OAGG;IACU,OAAO;IAmBpB;;;OAGG;IACH,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,KAAK,SAAS,GAEpB;IACD,OAAO,KAAK,SAAS,QAEpB;IACD;;;OAGG;IACH,OAAO,CAAC,OAAO,CAAS;IACxB,OAAO,KAAK,MAAM,GAEjB;IACD,OAAO,KAAK,MAAM,QAIjB;IAED;;OAEG;IACH,OAAO,CAAC,UAAU,CAAC,CAAa;IAChC,OAAO,CAAC,aAAa;YAaP,mBAAmB;IAcjC,OAAO,CAAC,gCAAgC,CAAS;IAEpC,iBAAiB;YAchB,oBAAoB;IAOlC;;;;;;OAMG;IACU,YAAY,CACvB,KAAK,EAAE,MAAM,EACb,eAAe,EAAE,mBAAmB,EACpC,WAAW,UAAQ;IAgCrB,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,cAAc;IAiBtB,OAAO,CAAC,oBAAoB;IAsB5B,OAAO,CAAC,aAAa;IAQrB,OAAO,CAAC,SAAS;IAYjB,OAAO,CAAC,kBAAkB,CAAoD;YAEhE,WAAW;YAyBX,eAAe;IAwB7B,OAAO,CAAC,4BAA4B,CAqBlC;IAEF,OAAO,CAAC,eAAe,CAIrB;IAEF,OAAO,CAAC,oBAAoB,CAG1B;IAEF,OAAO,CAAC,qBAAqB,CAG3B;IAEF,OAAO,CAAC,2BAA2B,CAEjC;IAEF,OAAO,CAAC,uBAAuB,CAE7B;IAEF,OAAO,CAAC,kBAAkB,CAIxB;CACH"}