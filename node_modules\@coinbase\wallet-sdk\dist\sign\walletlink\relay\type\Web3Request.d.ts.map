{"version": 3, "file": "Web3Request.d.ts", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/type/Web3Request.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAExF,MAAM,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AAChD,MAAM,MAAM,WAAW,CAAC,CAAC,SAAS,UAAU,GAAG,UAAU,IAAI,OAAO,CAAC,YAAY,EAAE;IAAE,MAAM,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC;AAElG,KAAK,YAAY,GACb;IACE,MAAM,EAAE,yBAAyB,CAAC;IAClC,MAAM,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;KAC3B,CAAC;CACH,GACD;IACE,MAAM,EAAE,8BAA8B,CAAC;CACxC,GACD;IACE,MAAM,EAAE,kBAAkB,CAAC;IAC3B,MAAM,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;QAC1B,MAAM,EAAE,MAAM,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,OAAO,EAAE,MAAM,CAAC;QAChB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,GAAG,EAAE,MAAM,CAAC;QACZ,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;CACH,GACD;IACE,MAAM,EAAE,kBAAkB,CAAC;IAC3B,MAAM,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;QAC7B,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;QACpB,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,cAAc,CAAC,EAAE;YACf,IAAI,EAAE,MAAM,CAAC;YACb,MAAM,EAAE,MAAM,CAAC;YACf,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;KACH,CAAC;CACH,GACD;IACE,MAAM,EAAE,qBAAqB,CAAC;IAC9B,MAAM,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;CACH,GACD;IACE,MAAM,EAAE,qBAAqB,CAAC;IAC9B,MAAM,EAAE;QACN,OAAO,EAAE,SAAS,CAAC;QACnB,OAAO,EAAE,aAAa,CAAC;QACvB,SAAS,EAAE,OAAO,CAAC;QACnB,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;KAC9B,CAAC;CACH,GACD;IACE,MAAM,EAAE,yBAAyB,CAAC;IAClC,MAAM,EAAE;QACN,WAAW,EAAE,aAAa,CAAC;QAC3B,SAAS,EAAE,aAAa,GAAG,IAAI,CAAC;QAChC,QAAQ,EAAE,YAAY,CAAC;QACvB,IAAI,EAAE,SAAS,CAAC;QAChB,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;QACxB,aAAa,EAAE,YAAY,GAAG,IAAI,CAAC;QACnC,YAAY,EAAE,YAAY,GAAG,IAAI,CAAC;QAClC,oBAAoB,EAAE,YAAY,GAAG,IAAI,CAAC;QAC1C,QAAQ,EAAE,YAAY,GAAG,IAAI,CAAC;QAC9B,OAAO,EAAE,MAAM,CAAC;QAChB,YAAY,EAAE,OAAO,CAAC;KACvB,CAAC;CACH,GACD;IACE,MAAM,EAAE,2BAA2B,CAAC;IACpC,MAAM,EAAE;QACN,iBAAiB,EAAE,SAAS,CAAC;QAC7B,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;CACH,GACD;IACE,MAAM,EAAE,kCAAkC,CAAC;IAC3C,MAAM,EAAE;QACN,OAAO,EAAE,SAAS,CAAC;QACnB,SAAS,EAAE,SAAS,CAAC;QACrB,SAAS,EAAE,OAAO,CAAC;KACpB,CAAC;CACH,GACD;IACE,MAAM,EAAE,YAAY,CAAC;IACrB,MAAM,EAAE;QACN,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC;YAChB,MAAM,CAAC,EAAE,MAAM,CAAC;YAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,KAAK,CAAC,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;CACH,CAAC"}