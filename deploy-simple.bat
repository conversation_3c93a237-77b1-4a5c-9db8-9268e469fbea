@echo off
echo ========================================
echo XMKF数字资产平台 - 简化版Reown AppKit部署
echo ========================================
echo.

echo 此版本使用CDN方式集成Reown AppKit，无需复杂的构建过程
echo.

:: 检查关键文件是否存在
echo 1. 检查文件完整性...

set "missing_files="
if not exist "public\hilltop\usdc\trade\index\usdc.html" set "missing_files=%missing_files% usdc.html"
if not exist "public\hilltop\usdc\trade\index\js\wallet-manager-simple.js" set "missing_files=%missing_files% wallet-manager-simple.js"

if not "%missing_files%"=="" (
    echo 错误: 以下文件缺失: %missing_files%
    echo 请确保所有必要文件都已创建
    pause
    exit /b 1
)

echo ✓ 所有关键文件检查完成
echo.

:: 创建备份
echo 2. 创建备份...
if not exist "backup" mkdir backup
if not exist "backup\%date:~0,10%" mkdir "backup\%date:~0,10%"

if exist "public\hilltop\usdc\trade\index\usdc-original-backup.html" (
    echo ✓ 原始文件备份已存在
) else (
    echo 警告: 未找到原始文件备份
)

echo.

:: 验证Reown AppKit集成
echo 3. 验证Reown AppKit集成...

:: 检查usdc.html中是否包含Reown AppKit引用
findstr /C:"@reown/appkit" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo ✓ Reown AppKit CDN引用已添加
) else (
    echo ❌ 未找到Reown AppKit CDN引用
    echo 请检查usdc.html文件是否正确修改
    pause
    exit /b 1
)

:: 检查钱包管理器脚本引用
findstr /C:"wallet-manager-simple.js" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo ✓ 钱包管理器脚本引用已添加
) else (
    echo ❌ 未找到钱包管理器脚本引用
    echo 请检查usdc.html文件是否正确修改
    pause
    exit /b 1
)

:: 检查钱包状态UI是否添加
findstr /C:"wallet-status" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo ✓ 钱包状态UI已添加
) else (
    echo ❌ 未找到钱包状态UI
    echo 请检查usdc.html文件是否正确修改
    pause
    exit /b 1
)

echo.

:: 检查网络连接
echo 4. 检查网络连接...
ping -n 1 unpkg.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 网络连接正常，可以访问CDN
) else (
    echo ⚠️  网络连接可能有问题，CDN资源可能无法加载
    echo 建议检查网络连接后再进行测试
)

echo.

:: 显示部署信息
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 已完成的集成:
echo - ✓ Reown AppKit CDN集成
echo - ✓ 简化版钱包管理器
echo - ✓ 钱包状态UI组件
echo - ✓ 与现有系统的兼容性适配
echo.
echo 支持的功能:
echo - 多钱包连接 (Trust Wallet, Coinbase Wallet, Kraken Wallet等)
echo - 多链支持 (Ethereum, BSC, Polygon)
echo - 与现有API的完美集成
echo - 移动端优化
echo.
echo 测试步骤:
echo 1. 在浏览器中打开: http://localhost/hilltop/usdc/trade/index/usdc.html
echo 2. 点击右上角的"连接钱包"按钮
echo 3. 选择您的钱包进行连接
echo 4. 验证钱包地址显示和余额信息
echo 5. 测试"授权交易"功能
echo.
echo 故障排除:
echo - 如果钱包连接失败，请检查网络连接和钱包应用状态
echo - 如果页面显示异常，请检查浏览器控制台错误信息
echo - 如果需要回滚，请运行: rollback-simple.bat
echo.

:: 询问是否立即测试
set /p test_now="是否立即在浏览器中打开测试页面? (y/n): "
if /i "%test_now%"=="y" (
    echo 正在打开测试页面...
    start "" "http://localhost/hilltop/usdc/trade/index/usdc.html"
    echo.
    echo 请在浏览器中测试以下功能:
    echo 1. 点击右上角"连接钱包"按钮
    echo 2. 选择钱包并连接
    echo 3. 验证地址显示和数据加载
    echo 4. 测试交易授权功能
)

echo.
echo ========================================
echo 技术支持信息
echo ========================================
echo.
echo 如果遇到问题，请检查:
echo 1. 浏览器控制台 (F12) 的错误信息
echo 2. 网络请求是否正常
echo 3. 钱包应用是否正常运行
echo.
echo 日志文件位置:
echo - 浏览器控制台日志
echo - 网络请求日志
echo.
echo 联系技术支持时，请提供:
echo - 浏览器类型和版本
echo - 钱包类型和版本
echo - 具体的错误信息
echo - 操作步骤
echo.

echo 简化版部署脚本执行完成！
pause
