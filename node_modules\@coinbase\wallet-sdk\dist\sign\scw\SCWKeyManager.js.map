{"version": 3, "file": "SCWKeyManager.js", "sourceRoot": "", "sources": ["../../../src/sign/scw/SCWKeyManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AACzE,OAAO,EACL,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,sBAAsB,GACvB,MAAM,iBAAiB,CAAC;AAMzB,MAAM,eAAe,GAAG;IACtB,UAAU,EAAE,eAAe;IAC3B,OAAO,EAAE,SAAS;CACV,CAAC;AACX,MAAM,cAAc,GAAG;IACrB,UAAU,EAAE,cAAc;IAC1B,OAAO,EAAE,QAAQ;CACT,CAAC;AACX,MAAM,eAAe,GAAG;IACtB,UAAU,EAAE,eAAe;IAC3B,OAAO,EAAE,QAAQ;CACT,CAAC;AAEX,MAAM,OAAO,aAAa;IAA1B;QACmB,YAAO,GAAG,IAAI,kBAAkB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACrE,kBAAa,GAAqB,IAAI,CAAC;QACvC,iBAAY,GAAqB,IAAI,CAAC;QACtC,kBAAa,GAAqB,IAAI,CAAC;QACvC,iBAAY,GAAqB,IAAI,CAAC;IA2EhD,CAAC;IAzEC,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,YAAa,CAAC;IAC5B,CAAC;IAED,uDAAuD;IACvD,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAc;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;QACzB,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QAC1C,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,UAAU,GAAG,MAAM,eAAe,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC;QACzC,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QAC5D,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC9D,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;gBAAE,OAAO;YACvE,IAAI,CAAC,YAAY,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,kBAAkB;IAEV,KAAK,CAAC,OAAO,CAAC,IAAiB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QAEtB,OAAO,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAiB,EAAE,GAAc;QACtD,MAAM,SAAS,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;CACF"}