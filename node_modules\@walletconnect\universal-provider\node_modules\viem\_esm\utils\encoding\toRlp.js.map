{"version": 3, "file": "toRlp.js", "sourceRoot": "", "sources": ["../../../utils/encoding/toRlp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAGhD,OAAO,EAGL,YAAY,GACb,MAAM,cAAc,CAAA;AAErB,OAAO,EAA4B,UAAU,EAAE,MAAM,cAAc,CAAA;AACnE,OAAO,EAA4B,UAAU,EAAE,MAAM,YAAY,CAAA;AAqBjE,MAAM,UAAU,KAAK,CACnB,KAAsD,EACtD,KAA0B,KAAK;IAE/B,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;IACrC,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;IAC7D,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAExB,IAAI,EAAE,KAAK,KAAK;QAAE,OAAO,UAAU,CAAC,MAAM,CAAC,KAAK,CAAwB,CAAA;IACxE,OAAO,MAAM,CAAC,KAA4B,CAAA;AAC5C,CAAC;AAID,MAAM,UAAU,UAAU,CACxB,KAAgC,EAChC,KAA0B,OAAO;IAEjC,OAAO,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AACzB,CAAC;AAID,MAAM,UAAU,QAAQ,CACtB,GAAwB,EACxB,KAA0B,KAAK;IAE/B,OAAO,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;AACvB,CAAC;AAED,SAAS,YAAY,CACnB,KAAsD;IAEtD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACtB,OAAO,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,OAAO,iBAAiB,CAAC,KAAY,CAAC,CAAA;AACxC,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAiB;IACzC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;IAE7D,MAAM,gBAAgB,GAAG,eAAe,CAAC,UAAU,CAAC,CAAA;IACpD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,CAAC,GAAG,UAAU,CAAA;QAC3C,OAAO,CAAC,GAAG,gBAAgB,GAAG,UAAU,CAAA;IAC1C,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,MAAM;QACN,MAAM,CAAC,MAAc;YACnB,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;gBACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC,CAAA;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,gBAAgB,CAAC,CAAA;gBAC7C,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;qBACnD,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;qBACzD,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;;oBACzD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;YACpC,CAAC;YACD,KAAK,MAAM,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;gBAC9B,MAAM,CAAC,MAAM,CAAC,CAAA;YAChB,CAAC;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,UAA2B;IACpD,MAAM,KAAK,GACT,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA;IAEtE,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IACvD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;YAAE,OAAO,CAAC,CAAA;QACnD,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE;YAAE,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;QAC/C,OAAO,CAAC,GAAG,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAA;IAC7C,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,MAAM;QACN,MAAM,CAAC,MAAc;YACnB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;gBAC1C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAA;gBACpC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,iBAAiB,CAAC,CAAA;gBAC9C,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;qBACtD,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;qBAC5D,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;;oBAC5D,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBACpC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,MAAc;IACrC,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC;QAAE,OAAO,CAAC,CAAA;IAC7B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAA;AAC7C,CAAC"}