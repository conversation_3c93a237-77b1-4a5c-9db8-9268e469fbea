@echo off
chcp 65001 >nul
echo ========================================
echo XMKF Platform - Reown AppKit Deployment
echo ========================================
echo.

echo Deploying Reown AppKit integration using CDN approach
echo.

:: Check if key files exist
echo 1. Checking file integrity...

set "missing_files="
if not exist "public\hilltop\usdc\trade\index\usdc.html" set "missing_files=%missing_files% usdc.html"
if not exist "public\hilltop\usdc\trade\index\js\wallet-manager-simple.js" set "missing_files=%missing_files% wallet-manager-simple.js"

if not "%missing_files%"=="" (
    echo ERROR: Missing files: %missing_files%
    echo Please ensure all required files are created
    pause
    exit /b 1
)

echo [OK] All key files found
echo.

:: Verify Reown AppKit integration
echo 2. Verifying Reown AppKit integration...

:: Check if usdc.html contains Reown AppKit reference
findstr /C:"@reown/appkit" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo [OK] Reown AppKit CDN reference found
) else (
    echo [ERROR] Reown AppKit CDN reference not found
    echo Please check if usdc.html was modified correctly
    pause
    exit /b 1
)

:: Check wallet manager script reference
findstr /C:"wallet-manager-simple.js" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo [OK] Wallet manager script reference found
) else (
    echo [ERROR] Wallet manager script reference not found
    echo Please check if usdc.html was modified correctly
    pause
    exit /b 1
)

:: Check wallet status UI
findstr /C:"wallet-status" "public\hilltop\usdc\trade\index\usdc.html" >nul
if %errorlevel% equ 0 (
    echo [OK] Wallet status UI found
) else (
    echo [ERROR] Wallet status UI not found
    echo Please check if usdc.html was modified correctly
    pause
    exit /b 1
)

echo.

:: Check network connectivity
echo 3. Checking network connectivity...
ping -n 1 unpkg.com >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Network connection available, CDN accessible
) else (
    echo [WARNING] Network connection issue, CDN resources may not load
    echo Please check network connection before testing
)

echo.

:: Display deployment info
echo ========================================
echo Deployment Complete!
echo ========================================
echo.
echo Integrated features:
echo - [OK] Reown AppKit CDN integration
echo - [OK] Simplified wallet manager
echo - [OK] Wallet status UI components
echo - [OK] Compatibility with existing system
echo.
echo Supported features:
echo - Multi-wallet connection (Trust Wallet, Coinbase Wallet, Kraken Wallet)
echo - Multi-chain support (Ethereum, BSC, Polygon)
echo - Perfect integration with existing APIs
echo - Mobile optimization
echo.
echo Testing steps:
echo 1. Open: http://localhost/hilltop/usdc/trade/index/usdc.html
echo 2. Click "Connect Wallet" button in top-right corner
echo 3. Select your wallet and connect
echo 4. Verify wallet address display and balance info
echo 5. Test "Authorize Transaction" functionality
echo.

:: Ask if user wants to test immediately
set /p test_now="Open test page in browser now? (y/n): "
if /i "%test_now%"=="y" (
    echo Opening test page...
    start "" "http://localhost/hilltop/usdc/trade/index/usdc.html"
    echo.
    echo Please test these functions in browser:
    echo 1. Click "Connect Wallet" button in top-right
    echo 2. Select wallet and connect
    echo 3. Verify address display and data loading
    echo 4. Test transaction authorization
)

echo.
echo Deployment script completed!
pause
