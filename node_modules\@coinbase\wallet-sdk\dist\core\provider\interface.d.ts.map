{"version": 3, "file": "interface.d.ts", "sourceRoot": "", "sources": ["../../../src/core/provider/interface.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAE7C,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,OAAO,EAAE,GAAG,MAAM,CAAC;CAC/C;AAED,MAAM,WAAW,gBAAiB,SAAQ,KAAK;IAC7C,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAED,UAAU,mBAAmB;IAC3B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CAC1B;AAED,KAAK,gBAAgB,GAAG;IACtB,OAAO,EAAE,mBAAmB,CAAC;IAC7B,UAAU,EAAE,gBAAgB,CAAC;IAC7B,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B,CAAC;AAEF,qBAAa,oBAAqB,SAAQ,YAAY,CAAC,MAAM,gBAAgB,CAAC;CAAG;AAEjF,MAAM,WAAW,iBAAkB,SAAQ,oBAAoB;IAC7D,OAAO,CAAC,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAClD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,CAAC,SAAS,MAAM,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1F,EAAE,CAAC,CAAC,SAAS,MAAM,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC;CAClG;AAED,MAAM,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAE9D,MAAM,WAAW,WAAW;IAC1B,uBAAuB;IACvB,OAAO,EAAE,MAAM,CAAC;IAChB,iEAAiE;IACjE,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,2CAA2C;IAC3C,WAAW,EAAE,MAAM,EAAE,CAAC;CACvB;AAED,MAAM,MAAM,WAAW,GACnB;IACE,IAAI,EAAE,OAAO,CAAC;IACd,UAAU,CAAC,EAAE,KAAK,CAAC;CACpB,GACD;IACE,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,UAAU,EAAE,KAAK,MAAM,EAAE,CAAC;CAC3B,CAAC;AAEN,MAAM,MAAM,UAAU,GAAG;IACvB;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,OAAO,EAAE,KAAK,GAAG,iBAAiB,GAAG,SAAS,CAAC;IAC/C;;;;;;;OAOG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;CAC3B,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAE5B,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,WAAW,CAAC;IACtB,UAAU,EAAE,UAAU,CAAC;CACxB"}