<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XMKF Reown AppKit 集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pass {
            background: #e8f5e8;
            color: #4caf50;
        }
        .status.fail {
            background: #ffebee;
            color: #f44336;
        }
        .status.pending {
            background: #fff3e0;
            color: #ff9800;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #1976d2;
            color: white;
        }
        .btn-primary:hover {
            background: #1565c0;
        }
        .btn-secondary {
            background: #757575;
            color: white;
        }
        .btn-secondary:hover {
            background: #616161;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .actions {
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 XMKF Reown AppKit 集成测试</h1>
            <p>验证Reown AppKit与现有系统的集成效果</p>
        </div>

        <div class="test-section">
            <h3>📋 系统检查</h3>
            <div class="test-item">
                <span>Reown AppKit库加载</span>
                <span id="appkit-status" class="status pending">检查中...</span>
            </div>
            <div class="test-item">
                <span>钱包管理器初始化</span>
                <span id="manager-status" class="status pending">检查中...</span>
            </div>
            <div class="test-item">
                <span>兼容性函数</span>
                <span id="compat-status" class="status pending">检查中...</span>
            </div>
            <div class="test-item">
                <span>网络连接</span>
                <span id="network-status" class="status pending">检查中...</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 钱包连接测试</h3>
            <div class="test-item">
                <span>当前连接状态</span>
                <span id="connection-status" class="status pending">未连接</span>
            </div>
            <div class="test-item">
                <span>钱包地址</span>
                <span id="wallet-address">-</span>
            </div>
            <div class="test-item">
                <span>网络信息</span>
                <span id="network-info">-</span>
            </div>
        </div>

        <div class="actions">
            <button id="connect-test" class="btn btn-primary">连接钱包测试</button>
            <button id="disconnect-test" class="btn btn-secondary">断开连接测试</button>
            <button id="refresh-test" class="btn btn-secondary">刷新状态</button>
        </div>

        <div class="test-section">
            <h3>📊 API集成测试</h3>
            <div class="test-item">
                <span>用户信息获取</span>
                <span id="api-status" class="status pending">待测试</span>
            </div>
            <div class="test-item">
                <span>余额显示</span>
                <span id="balance-status" class="status pending">待测试</span>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="test-log" class="log">
                等待测试开始...
            </div>
            <button id="clear-log" class="btn btn-secondary">清空日志</button>
            <button id="export-log" class="btn btn-secondary">导出日志</button>
        </div>

        <div class="actions">
            <button id="run-all-tests" class="btn btn-primary">运行所有测试</button>
            <button id="open-main-page" class="btn btn-secondary">打开主页面</button>
        </div>
    </div>

    <script>
        // 测试日志管理
        class TestLogger {
            constructor() {
                this.logElement = document.getElementById('test-log');
                this.logs = [];
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}`;
                this.logs.push(logEntry);
                
                this.logElement.innerHTML += logEntry + '\n';
                this.logElement.scrollTop = this.logElement.scrollHeight;
                
                console.log(logEntry);
            }

            clear() {
                this.logs = [];
                this.logElement.innerHTML = '';
            }

            export() {
                const logData = this.logs.join('\n');
                const blob = new Blob([logData], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `xmkf-test-log-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        const logger = new TestLogger();

        // 状态更新函数
        function updateStatus(elementId, status, text = null) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status ${status}`;
                if (text) element.textContent = text;
            }
        }

        // 系统检查
        async function runSystemChecks() {
            logger.log('开始系统检查...');

            // 检查Reown AppKit
            try {
                if (typeof createAppKit !== 'undefined') {
                    updateStatus('appkit-status', 'pass', '已加载');
                    logger.log('✓ Reown AppKit库已加载');
                } else {
                    updateStatus('appkit-status', 'fail', '未加载');
                    logger.log('✗ Reown AppKit库未加载');
                }
            } catch (error) {
                updateStatus('appkit-status', 'fail', '错误');
                logger.log('✗ Reown AppKit检查失败: ' + error.message);
            }

            // 检查钱包管理器
            try {
                if (typeof window.initSimpleWallet === 'function') {
                    updateStatus('manager-status', 'pass', '已就绪');
                    logger.log('✓ 钱包管理器已就绪');
                } else {
                    updateStatus('manager-status', 'fail', '未就绪');
                    logger.log('✗ 钱包管理器未就绪');
                }
            } catch (error) {
                updateStatus('manager-status', 'fail', '错误');
                logger.log('✗ 钱包管理器检查失败: ' + error.message);
            }

            // 检查兼容性函数
            try {
                const compatFunctions = ['onConnect', 'fetchAccountData'];
                let compatCount = 0;
                
                compatFunctions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        compatCount++;
                        logger.log(`✓ 兼容性函数 ${func} 存在`);
                    } else {
                        logger.log(`✗ 兼容性函数 ${func} 不存在`);
                    }
                });

                if (compatCount === compatFunctions.length) {
                    updateStatus('compat-status', 'pass', '完整');
                } else {
                    updateStatus('compat-status', 'fail', `${compatCount}/${compatFunctions.length}`);
                }
            } catch (error) {
                updateStatus('compat-status', 'fail', '错误');
                logger.log('✗ 兼容性检查失败: ' + error.message);
            }

            // 检查网络连接
            try {
                updateStatus('network-status', 'pass', '正常');
                logger.log('✓ 网络连接正常');
            } catch (error) {
                updateStatus('network-status', 'fail', '异常');
                logger.log('✗ 网络连接异常: ' + error.message);
            }

            logger.log('系统检查完成');
        }

        // 钱包连接测试
        async function testWalletConnection() {
            logger.log('开始钱包连接测试...');

            try {
                if (!window.simpleWallet) {
                    logger.log('初始化钱包管理器...');
                    await window.initSimpleWallet();
                }

                if (window.simpleWallet) {
                    await window.simpleWallet.connect();
                    logger.log('✓ 钱包连接请求已发送');
                } else {
                    throw new Error('钱包管理器未初始化');
                }
            } catch (error) {
                logger.log('✗ 钱包连接测试失败: ' + error.message);
            }
        }

        // 断开连接测试
        async function testWalletDisconnection() {
            logger.log('开始断开连接测试...');

            try {
                if (window.simpleWallet) {
                    await window.simpleWallet.disconnect();
                    logger.log('✓ 钱包断开连接成功');
                    updateConnectionStatus();
                } else {
                    throw new Error('钱包管理器未初始化');
                }
            } catch (error) {
                logger.log('✗ 断开连接测试失败: ' + error.message);
            }
        }

        // 更新连接状态
        function updateConnectionStatus() {
            try {
                if (window.simpleWallet && window.simpleWallet.isWalletConnected()) {
                    const address = window.simpleWallet.getCurrentAccount();
                    updateStatus('connection-status', 'pass', '已连接');
                    document.getElementById('wallet-address').textContent = 
                        address ? address.substring(0, 6) + '...' + address.substring(address.length - 4) : '-';
                    
                    logger.log('✓ 钱包已连接: ' + address);
                } else {
                    updateStatus('connection-status', 'fail', '未连接');
                    document.getElementById('wallet-address').textContent = '-';
                    logger.log('ℹ️ 钱包未连接');
                }
            } catch (error) {
                updateStatus('connection-status', 'fail', '错误');
                logger.log('✗ 连接状态检查失败: ' + error.message);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            logger.clear();
            logger.log('🚀 开始运行所有测试...');
            
            await runSystemChecks();
            updateConnectionStatus();
            
            logger.log('📋 所有测试完成');
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 按钮事件
            document.getElementById('connect-test').addEventListener('click', testWalletConnection);
            document.getElementById('disconnect-test').addEventListener('click', testWalletDisconnection);
            document.getElementById('refresh-test').addEventListener('click', updateConnectionStatus);
            document.getElementById('run-all-tests').addEventListener('click', runAllTests);
            document.getElementById('clear-log').addEventListener('click', () => logger.clear());
            document.getElementById('export-log').addEventListener('click', () => logger.export());
            
            document.getElementById('open-main-page').addEventListener('click', () => {
                window.open('/hilltop/usdc/trade/index/usdc.html', '_blank');
            });

            // 自动运行初始检查
            setTimeout(runAllTests, 1000);
        });

        // 监听钱包状态变化
        window.addEventListener('load', () => {
            if (window.simpleWallet) {
                window.simpleWallet.addEventListener('connected', (address) => {
                    logger.log('🔗 钱包连接事件: ' + address);
                    updateConnectionStatus();
                });

                window.simpleWallet.addEventListener('disconnected', () => {
                    logger.log('🔌 钱包断开事件');
                    updateConnectionStatus();
                });
            }
        });
    </script>
</body>
</html>
