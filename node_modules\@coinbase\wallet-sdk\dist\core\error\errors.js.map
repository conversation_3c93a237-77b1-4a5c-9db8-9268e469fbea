{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../src/core/error/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAEhD,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,GAAG,EAAE;QACH,KAAK,EAAE,CAAI,GAAqB,EAAE,EAAE,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC;QAE1F,cAAc,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC3C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC;QAEhE,aAAa,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC1C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;QAE/D,cAAc,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC3C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC;QAEhE,QAAQ,EAAE,CAAI,GAAqB,EAAE,EAAE,CACrC,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;QAE1D,MAAM,EAAE,CAAI,IAA2B,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACrF,CAAC;YACD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC9D,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;YACD,OAAO,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,CAAC;QAED,YAAY,EAAE,CAAI,GAAqB,EAAE,EAAE,CACzC,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC;QAE9D,gBAAgB,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC7C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;QAElE,mBAAmB,EAAE,CAAI,GAAqB,EAAE,EAAE,CAChD,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC;QAErE,mBAAmB,EAAE,CAAI,GAAqB,EAAE,EAAE,CAChD,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC;QAErE,kBAAkB,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC/C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC;QAEpE,aAAa,EAAE,CAAI,GAAqB,EAAE,EAAE,CAC1C,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;KAChE;IAED,QAAQ,EAAE;QACR,mBAAmB,EAAE,CAAI,GAAqB,EAAE,EAAE;YAChD,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;QACnF,CAAC;QAED,YAAY,EAAE,CAAI,GAAqB,EAAE,EAAE;YACzC,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC5E,CAAC;QAED,iBAAiB,EAAE,CAAI,GAAqB,EAAE,EAAE;YAC9C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACjF,CAAC;QAED,YAAY,EAAE,CAAI,GAAqB,EAAE,EAAE;YACzC,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC5E,CAAC;QAED,iBAAiB,EAAE,CAAI,GAAqB,EAAE,EAAE;YAC9C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACjF,CAAC;QAED,gBAAgB,EAAE,CAAI,GAAqB,EAAE,EAAE;YAC7C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,EAAE,CAAI,IAAuB,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;YAC1F,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;YAErC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YACD,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,CAAC;KACF;CACF,CAAC;AAEF,WAAW;AAEX,SAAS,kBAAkB,CAAI,IAAY,EAAE,GAAqB;IAChE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACvC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,mBAAmB,CAAI,IAAY,EAAE,GAAqB;IACjE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACvC,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACpF,CAAC;AAED,SAAS,SAAS,CAAI,GAAqB;IACzC,IAAI,GAAG,EAAE,CAAC;QACR,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;YAE9B,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC3C,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YACD,OAAO,CAAC,OAAO,IAAI,SAAS,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAeD,MAAM,gBAAoB,SAAQ,KAAK;IAKrC,YAAY,IAAY,EAAE,OAAe,EAAE,IAAQ;QACjD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;IACH,CAAC;CACF;AAED,MAAM,qBAAyB,SAAQ,gBAAmB;IACxD;;;OAGG;IACH,YAAY,IAAY,EAAE,OAAe,EAAE,IAAQ;QACjD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,CAAC;QAED,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;CACF;AAED,SAAS,sBAAsB,CAAC,IAAY;IAC1C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AAChE,CAAC"}