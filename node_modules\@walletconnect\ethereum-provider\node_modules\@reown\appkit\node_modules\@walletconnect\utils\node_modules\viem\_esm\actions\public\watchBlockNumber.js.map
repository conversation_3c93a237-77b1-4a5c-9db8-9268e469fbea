{"version": 3, "file": "watchBlockNumber.js", "sourceRoot": "", "sources": ["../../../actions/public/watchBlockNumber.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAA;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAA;AAChD,OAAO,EAAsB,IAAI,EAAE,MAAM,qBAAqB,CAAA;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AAEpD,OAAO,EAEL,cAAc,GACf,MAAM,qBAAqB,CAAA;AAwC5B;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,gBAAgB,CAI9B,MAAgC,EAChC,EACE,WAAW,GAAG,KAAK,EACnB,UAAU,GAAG,KAAK,EAClB,aAAa,EACb,OAAO,EACP,IAAI,EAAE,KAAK,EACX,eAAe,GAAG,MAAM,CAAC,eAAe,GACF;IAExC,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,WAAW;YAAE,OAAO,KAAK,CAAA;QAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW;YAAE,OAAO,KAAK,CAAA;QACvD,IACE,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU;YACpC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW;YAE1D,OAAO,KAAK,CAAA;QACd,OAAO,IAAI,CAAA;IACb,CAAC,CAAC,EAAE,CAAA;IAEJ,IAAI,eAAqD,CAAA;IAEzD,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,UAAU,GAAG,SAAS,CAAC;YAC3B,kBAAkB;YAClB,MAAM,CAAC,GAAG;YACV,WAAW;YACX,UAAU;YACV,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO,OAAO,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAC9D,IAAI,CACF,KAAK,IAAI,EAAE;YACT,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,SAAS,CACjC,MAAM,EACN,cAAc,EACd,gBAAgB,CACjB,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAA;gBAEnB,IAAI,eAAe,EAAE,CAAC;oBACpB,2DAA2D;oBAC3D,eAAe;oBACf,IAAI,WAAW,KAAK,eAAe;wBAAE,OAAM;oBAE3C,yDAAyD;oBACzD,wDAAwD;oBACxD,IAAI,WAAW,GAAG,eAAe,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;wBACpD,KAAK,IAAI,CAAC,GAAG,eAAe,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;4BACxD,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;4BACtC,eAAe,GAAG,CAAC,CAAA;wBACrB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,yDAAyD;gBACzD,+DAA+D;gBAC/D,IAAI,CAAC,eAAe,IAAI,WAAW,GAAG,eAAe,EAAE,CAAC;oBACtD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;oBAChD,eAAe,GAAG,WAAW,CAAA;gBAC/B,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,EACD;YACE,WAAW;YACX,QAAQ,EAAE,eAAe;SAC1B,CACF,CACF,CAAA;IACH,CAAC,CAAA;IAED,MAAM,oBAAoB,GAAG,GAAG,EAAE;QAChC,MAAM,UAAU,GAAG,SAAS,CAAC;YAC3B,kBAAkB;YAClB,MAAM,CAAC,GAAG;YACV,WAAW;YACX,UAAU;SACX,CAAC,CAAA;QAEF,OAAO,OAAO,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE;YAC9D,IAAI,MAAM,GAAG,IAAI,CAAA;YACjB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CACvC;YAAA,CAAC,KAAK,IAAI,EAAE;gBACX,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;wBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;4BACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAChD,CAAC,SAAgC,EAAE,EAAE,CACnC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CACxC,CAAA;4BACD,IAAI,CAAC,SAAS;gCAAE,OAAO,MAAM,CAAC,SAAS,CAAA;4BACvC,OAAO,SAAS,CAAC,KAAK,CAAA;wBACxB,CAAC;wBACD,OAAO,MAAM,CAAC,SAAS,CAAA;oBACzB,CAAC,CAAC,EAAE,CAAA;oBAEJ,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC;wBAC9D,MAAM,EAAE,CAAC,UAAU,CAAC;wBACpB,MAAM,CAAC,IAAS;4BACd,IAAI,CAAC,MAAM;gCAAE,OAAM;4BACnB,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;4BACpD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;4BAChD,eAAe,GAAG,WAAW,CAAA;wBAC/B,CAAC;wBACD,OAAO,CAAC,KAAY;4BAClB,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;wBACvB,CAAC;qBACF,CAAC,CAAA;oBACF,WAAW,GAAG,YAAY,CAAA;oBAC1B,IAAI,CAAC,MAAM;wBAAE,WAAW,EAAE,CAAA;gBAC5B,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;gBACzB,CAAC;YACH,CAAC,CAAC,EAAE,CAAA;YACJ,OAAO,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;QAC5B,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAA;AACnE,CAAC"}