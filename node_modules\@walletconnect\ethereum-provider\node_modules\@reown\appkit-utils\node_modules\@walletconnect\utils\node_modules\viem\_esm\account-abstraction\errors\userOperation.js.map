{"version": 3, "file": "userOperation.js", "sourceRoot": "", "sources": ["../../../account-abstraction/errors/userOperation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAA;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AAMjD,MAAM,OAAO,2BAA4B,SAAQ,SAAS;IAGxD,YACE,KAAgB,EAChB,EACE,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,kBAAkB,EAClB,MAAM,EACN,SAAS,EACT,oBAAoB,GAGrB;QAED,MAAM,UAAU,GAAG,WAAW,CAAC;YAC7B,QAAQ;YACR,YAAY;YACZ,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY,EACV,OAAO,YAAY,KAAK,WAAW;gBACnC,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO;YACpC,oBAAoB,EAClB,OAAO,oBAAoB,KAAK,WAAW;gBAC3C,GAAG,UAAU,CAAC,oBAAoB,CAAC,OAAO;YAC5C,KAAK;YACL,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,MAAM;YACN,SAAS;YACT,oBAAoB;SACrB,CAAC,CAAA;QAEF,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE;YACxB,KAAK;YACL,QAAQ;YACR,YAAY,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3D,oBAAoB;gBACpB,UAAU;aACX,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAA;QA5DK;;;;;WAAgB;QA6DvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;CACF;AAMD,MAAM,OAAO,iCAAkC,SAAQ,SAAS;IAC9D,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,qCAAqC,IAAI,2EAA2E,EACpH,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAC9C,CAAA;IACH,CAAC;CACF;AAKD,MAAM,OAAO,0BAA2B,SAAQ,SAAS;IACvD,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CAAC,6BAA6B,IAAI,uBAAuB,EAAE;YAC9D,IAAI,EAAE,4BAA4B;SACnC,CAAC,CAAA;IACJ,CAAC;CACF;AAMD,MAAM,OAAO,uCAAwC,SAAQ,SAAS;IACpE,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,yDAAyD,IAAI,oBAAoB,EACjF,EAAE,IAAI,EAAE,yCAAyC,EAAE,CACpD,CAAA;IACH,CAAC;CACF"}