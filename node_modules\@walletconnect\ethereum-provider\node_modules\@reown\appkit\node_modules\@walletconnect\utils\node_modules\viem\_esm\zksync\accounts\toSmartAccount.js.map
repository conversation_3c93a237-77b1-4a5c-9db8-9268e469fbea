{"version": 3, "file": "toSmartAccount.js", "sourceRoot": "", "sources": ["../../../zksync/accounts/toSmartAccount.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAA;AAGvD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,sCAAsC,CAAA;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wCAAwC,CAAA;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAA;AAaxD;;;GAGG;AACH,MAAM,UAAU,cAAc,CAC5B,UAAoC;IAEpC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,UAAU,CAAA;IAEpC,MAAM,OAAO,GAAG,SAAS,CAAC;QACxB,OAAO;QACP,IAAI;QACJ,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC;aAC3B,CAAC,CAAA;QACJ,CAAC;QACD,KAAK,CAAC,eAAe,CAAC,WAAW;YAC/B,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,WAAW;gBACd,IAAI,EAAE,IAAI,CAAC,OAAQ;aACmB,CAAA;YAExC,OAAO,oBAAoB,CAAC;gBAC1B,GAAG,mBAAmB;gBACtB,eAAe,EAAE,MAAM,IAAI,CAAC;oBAC1B,IAAI,EAAE,SAAS,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;iBAC3D,CAAC;aACH,CAAC,CAAA;QACJ,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,SAAS;YAC3B,OAAO,IAAI,CAAC;gBACV,IAAI,EAAE,aAAa,CAAC,SAAS,CAAC;aAC/B,CAAC,CAAA;QACJ,CAAC;KACF,CAAC,CAAA;IAEF,OAAO;QACL,GAAG,OAAO;QACV,MAAM,EAAE,oBAAoB;KACP,CAAA;AACzB,CAAC"}