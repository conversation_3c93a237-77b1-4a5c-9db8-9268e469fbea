{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/core/error/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAEjE,MAAM,gBAAgB,GAAG,4BAA4B,CAAC;AAEtD,MAAM,CAAC,MAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAIzE;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAChC,IAAwB,EACxB,kBAA0B,gBAAgB;IAE1C,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEnC,IAAI,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC;YACpC,OAAO,WAAW,CAAC,UAA2B,CAAC,CAAC,OAAO,CAAC;QAC1D,CAAC;QACD,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,OAAO,6BAA6B,CAAC;QACvC,CAAC;IACH,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IACnC,IAAI,WAAW,CAAC,UAA2B,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAAc;;IACzC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,MAAA,KAAK,CAAC,IAAI,mCAAI,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAOD,SAAS,eAAe,CAAC,KAAc;IACrC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,CAAC,OAAQ,KAAuB,CAAC,IAAI,KAAK,QAAQ;YAChD,OAAQ,KAAuB,CAAC,SAAS,KAAK,QAAQ,CAAC,CAC1D,CAAC;AACJ,CAAC;AAgBD,MAAM,UAAU,SAAS,CACvB,KAAc,EACd,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,EAAE;IAEnC,MAAM,UAAU,GAAwC,EAAE,CAAC;IAE3D,IACE,KAAK;QACL,OAAO,KAAK,KAAK,QAAQ;QACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACrB,MAAM,CAAC,KAAgC,EAAE,MAAM,CAAC;QAChD,WAAW,CAAE,KAAoC,CAAC,IAAI,CAAC,EACvD,CAAC;QACD,MAAM,MAAM,GAAG,KAA4C,CAAC;QAC5D,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAE9B,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACzD,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAEpC,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;gBAC3B,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YAChC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,OAAO,GAAG,kBAAkB,CAAE,UAAyC,CAAC,IAAI,CAAC,CAAC;YAEzF,UAAU,CAAC,IAAI,GAAG,EAAE,aAAa,EAAE,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;QAClE,CAAC;IACH,CAAC;SAAM,CAAC;QACN,UAAU,CAAC,IAAI,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;QAElD,UAAU,CAAC,OAAO,GAAG,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAC5F,UAAU,CAAC,IAAI,GAAG,EAAE,aAAa,EAAE,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;IAClE,CAAC;IAED,IAAI,kBAAkB,EAAE,CAAC;QACvB,UAAU,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IACjF,CAAC;IACD,OAAO,UAAwC,CAAC;AAClD,CAAC;AAED,WAAW;AAEX,SAAS,oBAAoB,CAAC,IAAY;IACxC,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC;AAC1C,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAc;IACzC,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAChE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAC,GAA4B,EAAE,GAAW;IACvD,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC;AAED,SAAS,iBAAiB,CAAI,GAAY,EAAE,IAAa;IACvD,OAAO,CACL,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,OAAQ,GAAS,CAAC,IAAI,CAAC,KAAK,QAAQ,CAC/F,CAAC;AACJ,CAAC"}