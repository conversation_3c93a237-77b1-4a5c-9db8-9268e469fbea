// XMKF数字资产平台 - 简化版钱包管理器
// 使用CDN方式集成Reown AppKit，无需复杂构建

// 钱包配置
const WALLET_CONFIG = {
  projectId: '1a54ba92caa7810745990910f7daccc4',
  
  metadata: {
    name: 'XMKF Digital Asset Platform',
    description: 'XMKF数字资产管理平台',
    url: window.location.origin,
    icons: [window.location.origin + '/favicon.ico']
  },

  // 支持的网络
  networks: [
    {
      chainId: 1,
      name: 'Ethereum',
      currency: 'ETH',
      explorerUrl: 'https://etherscan.io',
      rpcUrl: 'https://mainnet.infura.io/v3/********************************'
    },
    {
      chainId: 56,
      name: 'BNB Smart Chain',
      currency: 'BNB',
      explorerUrl: 'https://bscscan.com',
      rpcUrl: 'https://bsc-dataseed.binance.org'
    },
    {
      chainId: 137,
      name: 'Polygon',
      currency: 'MATIC',
      explorerUrl: 'https://polygonscan.com',
      rpcUrl: 'https://polygon-rpc.com'
    }
  ],

  features: {
    analytics: true,
    email: false,
    socials: [],
    onramp: false,
    swaps: false
  },

  themeMode: 'light',
  themeVariables: {
    '--w3m-accent': '#1976d2',
    '--w3m-background-color': '#ffffff'
  }
};

// 简化版钱包管理器类
class SimpleWalletManager {
  constructor() {
    this.appKit = null;
    this.currentAccount = null;
    this.currentNetwork = null;
    this.isConnected = false;
    this.eventListeners = {
      accountChanged: [],
      networkChanged: [],
      connected: [],
      disconnected: []
    };
  }

  // 初始化钱包管理器
  async init() {
    try {
      console.log('初始化简化版钱包管理器...');

      // 等待AppKit库加载
      if (typeof createAppKit === 'undefined') {
        console.log('等待AppKit库加载...');
        await this.waitForAppKit();
      }

      // 创建AppKit实例
      this.appKit = createAppKit({
        projectId: WALLET_CONFIG.projectId,
        metadata: WALLET_CONFIG.metadata,
        networks: WALLET_CONFIG.networks,
        features: WALLET_CONFIG.features,
        themeMode: WALLET_CONFIG.themeMode,
        themeVariables: WALLET_CONFIG.themeVariables
      });

      // 设置事件监听
      this.setupEventListeners();

      console.log('简化版钱包管理器初始化完成');
      return true;
    } catch (error) {
      console.error('钱包管理器初始化失败:', error);
      return false;
    }
  }

  // 等待AppKit库加载
  waitForAppKit() {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 50; // 最多等待5秒

      const checkAppKit = () => {
        if (typeof createAppKit !== 'undefined') {
          resolve();
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(checkAppKit, 100);
        } else {
          reject(new Error('AppKit库加载超时'));
        }
      };

      checkAppKit();
    });
  }

  // 设置事件监听器
  setupEventListeners() {
    if (!this.appKit) return;

    // 监听账户变化
    this.appKit.subscribeAccount((account) => {
      console.log('账户变化:', account);
      
      if (account.address !== this.currentAccount) {
        this.currentAccount = account.address;
        this.isConnected = !!account.address;
        
        if (account.address) {
          localStorage.setItem('walletAddress', account.address);
          this.triggerEvent('accountChanged', account.address);
          this.triggerEvent('connected', account.address);
          this.updateUI(account.address);
        } else {
          localStorage.removeItem('walletAddress');
          this.triggerEvent('disconnected');
          this.updateUI(null);
        }
      }
    });

    // 监听网络变化
    this.appKit.subscribeNetwork((network) => {
      console.log('网络变化:', network);
      
      if (network.chainId !== this.currentNetwork) {
        this.currentNetwork = network.chainId;
        this.triggerEvent('networkChanged', network);
      }
    });
  }

  // 连接钱包
  async connect() {
    try {
      console.log('开始连接钱包...');
      
      if (!this.appKit) {
        throw new Error('钱包管理器未初始化');
      }

      await this.appKit.open();
      return true;
    } catch (error) {
      console.error('钱包连接失败:', error);
      throw error;
    }
  }

  // 断开钱包连接
  async disconnect() {
    try {
      console.log('断开钱包连接...');
      
      if (this.appKit) {
        await this.appKit.disconnect();
      }
      
      this.currentAccount = null;
      this.isConnected = false;
      localStorage.removeItem('walletAddress');
      
      this.triggerEvent('disconnected');
      this.updateUI(null);
      return true;
    } catch (error) {
      console.error('断开连接失败:', error);
      throw error;
    }
  }

  // 获取当前账户
  getCurrentAccount() {
    return this.currentAccount || localStorage.getItem('walletAddress');
  }

  // 检查连接状态
  isWalletConnected() {
    return this.isConnected && !!this.getCurrentAccount();
  }

  // 切换网络
  async switchNetwork(chainId) {
    try {
      if (!this.appKit) {
        throw new Error('钱包管理器未初始化');
      }

      await this.appKit.open({ view: 'Networks' });
      return true;
    } catch (error) {
      console.error('网络切换失败:', error);
      throw error;
    }
  }

  // 更新UI
  updateUI(address) {
    // 更新连接状态显示
    const connectBtn = document.getElementById('connect-btn');
    const walletInfo = document.getElementById('wallet-info');
    const walletAdd = document.getElementById('walletadd');

    if (address) {
      // 已连接状态
      if (connectBtn) connectBtn.style.display = 'none';
      if (walletInfo) walletInfo.style.display = 'block';
      if (walletAdd) {
        const shortAddress = address.substring(0, 6) + '...' + address.substring(address.length - 4);
        walletAdd.textContent = shortAddress;
      }

      // 调用API获取用户信息
      this.fetchUserData(address);
    } else {
      // 未连接状态
      if (connectBtn) connectBtn.style.display = 'block';
      if (walletInfo) walletInfo.style.display = 'none';
      if (walletAdd) walletAdd.textContent = '未连接';

      // 清空余额显示
      this.clearBalanceDisplay();
    }
  }

  // 获取用户数据
  async fetchUserData(address) {
    try {
      const response = await fetch('/api/get_info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `address=${encodeURIComponent(address)}`
      });

      const result = await response.json();
      console.log('API响应:', result);

      if (result.code === 200) {
        this.updateBalanceDisplay(result.data);
      }

      return result;
    } catch (error) {
      console.error('获取用户数据失败:', error);
      throw error;
    }
  }

  // 更新余额显示
  updateBalanceDisplay(data) {
    const elements = {
      'plat_balance': data.plat_balance + ' USDT',
      'plat_balance_num': data.plat_balance,
      'stack_amount': data.stack_amount + ' USDT',
      'exchange': data.plat_erc + ' ETH',
      'exchange_num': data.plat_erc,
      'stack_income': data.stack_income
    };

    Object.keys(elements).forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = elements[id];
      }
    });

    // 处理其他字段
    if (data.p_address) {
      const fidElement = document.getElementById('fid');
      if (fidElement) {
        fidElement.value = data.p_address;
        fidElement.setAttribute('readonly', 'readonly');
      }
    }

    if (data.share_link) {
      const shareLinkElement = document.getElementById('share_link');
      if (shareLinkElement) {
        shareLinkElement.value = data.share_link;
      }
    }
  }

  // 清空余额显示
  clearBalanceDisplay() {
    const balanceElements = ['plat_balance', 'stack_amount', 'exchange', 'stack_income'];
    balanceElements.forEach(id => {
      const element = document.getElementById(id);
      if (element) element.textContent = '0';
    });
  }

  // 添加事件监听器
  addEventListener(event, callback) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(callback);
    }
  }

  // 触发事件
  triggerEvent(event, data = null) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('事件回调执行失败:', error);
        }
      });
    }
  }
}

// 全局初始化函数
window.initSimpleWallet = async function() {
  try {
    console.log('初始化简化版钱包系统...');
    
    const walletManager = new SimpleWalletManager();
    const initSuccess = await walletManager.init();
    
    if (!initSuccess) {
      throw new Error('钱包管理器初始化失败');
    }

    window.simpleWallet = walletManager;
    console.log('简化版钱包系统初始化完成');
    return walletManager;
  } catch (error) {
    console.error('简化版钱包系统初始化失败:', error);
    throw error;
  }
};

// 兼容性函数
window.onConnect = async function() {
  try {
    if (!window.simpleWallet) {
      await window.initSimpleWallet();
    }
    await window.simpleWallet.connect();
    return true;
  } catch (error) {
    console.error('连接钱包失败:', error);
    return false;
  }
};

window.fetchAccountData = async function() {
  try {
    if (window.simpleWallet) {
      const account = window.simpleWallet.getCurrentAccount();
      if (account) {
        await window.simpleWallet.fetchUserData(account);
      }
    }
    return true;
  } catch (error) {
    console.error('获取账户数据失败:', error);
    return false;
  }
};
