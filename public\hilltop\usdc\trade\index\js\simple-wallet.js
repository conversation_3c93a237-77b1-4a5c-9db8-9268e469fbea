// XMKF数字资产平台 - 简单钱包连接实现
// 使用更可靠的方法进行钱包连接

console.log('🚀 简单钱包连接脚本开始加载...');

// 全局变量
let simpleWallet = null;

// 测试日志函数
function simpleLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    
    // 显示在调试面板
    const logElement = document.getElementById('test-log');
    if (logElement) {
        logElement.innerHTML += logMessage + '\n';
        logElement.scrollTop = logElement.scrollHeight;
    }
}

// 简单钱包管理器
class SimpleWalletConnector {
    constructor() {
        this.isConnected = false;
        this.currentAccount = null;
        this.provider = null;
        this.web3 = null;
        this.eventListeners = {
            connected: [],
            disconnected: [],
            accountChanged: []
        };
        simpleLog('SimpleWalletConnector 初始化完成');
    }

    // 检查是否有注入的钱包
    checkInjectedWallet() {
        simpleLog('检查注入的钱包...');
        
        if (typeof window.ethereum !== 'undefined') {
            simpleLog('✓ 检测到 window.ethereum');
            return true;
        }
        
        if (typeof window.web3 !== 'undefined') {
            simpleLog('✓ 检测到 window.web3');
            return true;
        }
        
        simpleLog('✗ 未检测到注入的钱包');
        return false;
    }

    // 连接钱包
    async connect() {
        simpleLog('开始连接钱包...');
        
        try {
            // 检查是否有注入的钱包
            if (!this.checkInjectedWallet()) {
                throw new Error('未检测到钱包，请安装MetaMask、Trust Wallet或其他Web3钱包');
            }

            // 使用 window.ethereum
            if (window.ethereum) {
                simpleLog('使用 window.ethereum 连接...');
                this.provider = window.ethereum;
                
                // 请求连接
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });
                
                if (accounts.length > 0) {
                    this.currentAccount = accounts[0];
                    this.isConnected = true;
                    
                    // 保存到localStorage
                    localStorage.setItem('walletAddress', this.currentAccount);
                    
                    simpleLog(`✓ 钱包连接成功: ${this.currentAccount}`);
                    
                    // 设置事件监听
                    this.setupEventListeners();
                    
                    // 触发连接事件
                    this.triggerEvent('connected', this.currentAccount);
                    
                    return this.currentAccount;
                } else {
                    throw new Error('未获取到账户地址');
                }
            } else {
                throw new Error('不支持的钱包类型');
            }
        } catch (error) {
            simpleLog(`✗ 钱包连接失败: ${error.message}`);
            throw error;
        }
    }

    // 设置事件监听
    setupEventListeners() {
        if (!this.provider) return;

        simpleLog('设置钱包事件监听...');

        // 监听账户变化
        this.provider.on('accountsChanged', (accounts) => {
            simpleLog(`账户变化事件: ${JSON.stringify(accounts)}`);
            
            if (accounts.length > 0) {
                const newAccount = accounts[0];
                if (newAccount !== this.currentAccount) {
                    this.currentAccount = newAccount;
                    localStorage.setItem('walletAddress', newAccount);
                    this.triggerEvent('accountChanged', newAccount);
                    simpleLog(`账户已切换到: ${newAccount}`);
                }
            } else {
                this.disconnect();
            }
        });

        // 监听链变化
        this.provider.on('chainChanged', (chainId) => {
            simpleLog(`网络变化事件: ${chainId}`);
            // 可以在这里处理网络变化
        });

        // 监听连接状态
        this.provider.on('connect', (connectInfo) => {
            simpleLog(`连接事件: ${JSON.stringify(connectInfo)}`);
        });

        // 监听断开连接
        this.provider.on('disconnect', (error) => {
            simpleLog(`断开连接事件: ${JSON.stringify(error)}`);
            this.disconnect();
        });
    }

    // 断开连接
    async disconnect() {
        simpleLog('断开钱包连接...');
        
        this.currentAccount = null;
        this.isConnected = false;
        this.provider = null;
        localStorage.removeItem('walletAddress');
        
        this.triggerEvent('disconnected');
        simpleLog('✓ 钱包已断开连接');
    }

    // 获取当前账户
    getCurrentAccount() {
        return this.currentAccount || localStorage.getItem('walletAddress');
    }

    // 检查连接状态
    isWalletConnected() {
        return this.isConnected && !!this.getCurrentAccount();
    }

    // 获取网络信息
    async getNetworkInfo() {
        if (!this.provider) return null;

        try {
            const chainId = await this.provider.request({ method: 'eth_chainId' });
            simpleLog(`当前网络 Chain ID: ${chainId}`);
            return { chainId };
        } catch (error) {
            simpleLog(`获取网络信息失败: ${error.message}`);
            return null;
        }
    }

    // 切换网络
    async switchNetwork(chainId) {
        if (!this.provider) {
            throw new Error('钱包未连接');
        }

        try {
            simpleLog(`请求切换到网络: ${chainId}`);
            
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: chainId }],
            });
            
            simpleLog(`✓ 网络切换成功: ${chainId}`);
        } catch (error) {
            simpleLog(`✗ 网络切换失败: ${error.message}`);
            throw error;
        }
    }

    // 添加事件监听
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }

    // 触发事件
    triggerEvent(event, data = null) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    simpleLog(`事件回调执行失败: ${error.message}`);
                }
            });
        }
    }
}

// 初始化简单钱包
async function initSimpleWallet() {
    simpleLog('初始化简单钱包连接器...');
    
    try {
        simpleWallet = new SimpleWalletConnector();
        
        // 设置事件监听
        simpleWallet.addEventListener('connected', (address) => {
            simpleLog(`钱包连接事件触发: ${address}`);
            updateSimpleUI(address);
            
            // 更新原有的全局变量以保持兼容性
            if (typeof selectedAccount !== 'undefined') {
                selectedAccount = address;
            }
            if (typeof xlit !== 'undefined') {
                xlit = 1;
            }

            // 调用原有的API获取用户信息
            if (typeof fetchAccountData === 'function') {
                fetchAccountData();
            }
        });
        
        simpleWallet.addEventListener('disconnected', () => {
            simpleLog('钱包断开事件触发');
            updateSimpleUI(null);
            
            // 更新原有的全局变量
            if (typeof selectedAccount !== 'undefined') {
                selectedAccount = null;
            }
            if (typeof xlit !== 'undefined') {
                xlit = 0;
            }
        });

        simpleWallet.addEventListener('accountChanged', (address) => {
            simpleLog(`账户变化事件触发: ${address}`);
            updateSimpleUI(address);
            
            // 更新原有的全局变量
            if (typeof selectedAccount !== 'undefined') {
                selectedAccount = address;
            }

            // 调用原有的API获取用户信息
            if (typeof fetchAccountData === 'function') {
                fetchAccountData();
            }
        });
        
        // 检查是否有已保存的连接
        const savedAddress = localStorage.getItem('walletAddress');
        if (savedAddress) {
            simpleLog(`检测到已保存的地址: ${savedAddress}`);
            // 尝试重新连接验证
            if (simpleWallet.checkInjectedWallet()) {
                simpleLog('尝试重新验证钱包连接...');
                try {
                    const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                    if (accounts.includes(savedAddress)) {
                        simpleWallet.currentAccount = savedAddress;
                        simpleWallet.isConnected = true;
                        simpleWallet.provider = window.ethereum;
                        simpleWallet.setupEventListeners();
                        updateSimpleUI(savedAddress);
                        simpleLog('✓ 钱包连接状态已恢复');
                    }
                } catch (error) {
                    simpleLog(`连接状态验证失败: ${error.message}`);
                }
            }
        }
        
        simpleLog('✓ 简单钱包连接器初始化完成');
        return simpleWallet;
    } catch (error) {
        simpleLog(`✗ 简单钱包连接器初始化失败: ${error.message}`);
        throw error;
    }
}

// 更新UI显示
function updateSimpleUI(address) {
    simpleLog(`更新UI显示: ${address || '未连接'}`);
    
    // 更新连接按钮
    const connectBtn = document.getElementById('connect-wallet-btn');
    const walletInfo = document.getElementById('wallet-info');
    const walletAddress = document.getElementById('wallet-address');
    
    if (address) {
        // 已连接状态
        if (connectBtn) {
            connectBtn.style.display = 'none';
            simpleLog('隐藏连接按钮');
        }
        if (walletInfo) {
            walletInfo.style.display = 'block';
            simpleLog('显示钱包信息');
        }
        if (walletAddress) {
            const shortAddress = address.substring(0, 6) + '...' + address.substring(address.length - 4);
            walletAddress.textContent = shortAddress;
            simpleLog(`设置钱包地址显示: ${shortAddress}`);
        }
        
        // 更新原有的walletadd元素
        const walletAddElement = document.getElementById('walletadd');
        if (walletAddElement) {
            walletAddElement.innerHTML = address.substring(0, 5);
            simpleLog('更新原有walletadd元素');
        }
    } else {
        // 未连接状态
        if (connectBtn) {
            connectBtn.style.display = 'block';
            simpleLog('显示连接按钮');
        }
        if (walletInfo) {
            walletInfo.style.display = 'none';
            simpleLog('隐藏钱包信息');
        }
        if (walletAddress) {
            walletAddress.textContent = '未连接';
        }
        
        // 更新原有的walletadd元素
        const walletAddElement = document.getElementById('walletadd');
        if (walletAddElement) {
            walletAddElement.innerHTML = '未连接';
        }
    }
}

// 设置按钮事件监听
function setupSimpleEventListeners() {
    simpleLog('设置简单钱包按钮事件监听...');
    
    // 连接钱包按钮
    const connectBtn = document.getElementById('connect-wallet-btn');
    if (connectBtn) {
        connectBtn.addEventListener('click', async () => {
            simpleLog('用户点击连接钱包按钮');
            try {
                if (simpleWallet) {
                    await simpleWallet.connect();
                } else {
                    simpleLog('简单钱包未初始化，尝试初始化...');
                    await initSimpleWallet();
                    await simpleWallet.connect();
                }
            } catch (error) {
                simpleLog(`连接失败: ${error.message}`);
                alert('连接钱包失败: ' + error.message);
            }
        });
        simpleLog('✓ 连接按钮事件监听已设置');
    } else {
        simpleLog('✗ 未找到连接按钮元素');
    }
    
    // 断开连接按钮
    const disconnectBtn = document.getElementById('disconnect-btn');
    if (disconnectBtn) {
        disconnectBtn.addEventListener('click', async () => {
            simpleLog('用户点击断开连接按钮');
            try {
                if (simpleWallet) {
                    await simpleWallet.disconnect();
                } else {
                    simpleLog('简单钱包未初始化');
                }
            } catch (error) {
                simpleLog(`断开连接失败: ${error.message}`);
            }
        });
        simpleLog('✓ 断开按钮事件监听已设置');
    } else {
        simpleLog('✗ 未找到断开按钮元素');
    }

    // 网络切换按钮
    const networkBtn = document.getElementById('switch-network-btn');
    if (networkBtn) {
        networkBtn.addEventListener('click', async () => {
            simpleLog('用户点击切换网络按钮');
            try {
                if (simpleWallet && simpleWallet.isWalletConnected()) {
                    // 显示网络选择选项
                    const networks = [
                        { name: 'Ethereum', chainId: '0x1' },
                        { name: 'BSC', chainId: '0x38' },
                        { name: 'Polygon', chainId: '0x89' }
                    ];
                    
                    const networkNames = networks.map(n => n.name).join('\n');
                    const choice = prompt(`选择网络:\n${networkNames}\n\n请输入网络名称:`);
                    
                    const selectedNetwork = networks.find(n => n.name.toLowerCase() === choice?.toLowerCase());
                    if (selectedNetwork) {
                        await simpleWallet.switchNetwork(selectedNetwork.chainId);
                    }
                } else {
                    simpleLog('钱包未连接');
                    alert('请先连接钱包');
                }
            } catch (error) {
                simpleLog(`网络切换失败: ${error.message}`);
                alert('网络切换失败: ' + error.message);
            }
        });
        simpleLog('✓ 网络切换按钮事件监听已设置');
    }
}

// 兼容性函数
window.simpleOnConnect = async function() {
    simpleLog('调用兼容性函数 simpleOnConnect');
    try {
        if (!simpleWallet) {
            await initSimpleWallet();
        }
        await simpleWallet.connect();
        return true;
    } catch (error) {
        simpleLog(`simpleOnConnect 失败: ${error.message}`);
        return false;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    simpleLog('DOM内容加载完成，开始初始化简单钱包...');
    
    // 延迟初始化，确保所有元素都已加载
    setTimeout(async () => {
        try {
            await initSimpleWallet();
            setupSimpleEventListeners();
            simpleLog('🎉 简单钱包系统初始化完成！');
        } catch (error) {
            simpleLog(`❌ 初始化失败: ${error.message}`);
        }
    }, 1000);
});

// 如果页面已经加载完成
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    simpleLog('页面已加载完成，立即初始化简单钱包...');
    setTimeout(async () => {
        try {
            await initSimpleWallet();
            setupSimpleEventListeners();
            simpleLog('🎉 简单钱包系统初始化完成！');
        } catch (error) {
            simpleLog(`❌ 初始化失败: ${error.message}`);
        }
    }, 500);
}

simpleLog('📝 简单钱包连接脚本加载完成');

// 导出到全局作用域
window.simpleWallet = simpleWallet;
window.initSimpleWallet = initSimpleWallet;
