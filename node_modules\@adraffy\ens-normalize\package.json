{"name": "@adraffy/ens-normalize", "version": "1.11.0", "description": "Ethereum Name Service (ENS) Name Normalizer", "keywords": ["ENS", "ENSIP-1", "ENSIP-15", "Ethereum", "UTS-46", "UTS-51", "IDNA", "Name", "Normalize", "Normalization", "NFC", "NFD"], "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./xnf": {"import": "./dist/index-xnf.mjs", "require": "./dist/index-xnf.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "files": ["./dist"], "repository": {"type": "git", "url": "git+https://github.com/adraffy/ens-normalize.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/adraffy/ens-normalize.js/issues"}, "homepage": "https://github.com/adraffy/ens-normalize.js#readme", "author": {"name": "raffy.eth", "url": "http://raffy.antistupid.com"}, "scripts": {"unicode": "node derive/download.js", "labels": "node validate/download-labels.js", "derive": "node derive/make.js", "make": "node src/make.js", "validate": "node validate/make.js", "test": "node test/all.js", "types": "cp dist/index.d.mts dist/index.d.cts && cp dist/index.d.mts dist/index-xnf.d.mts && cp dist/index.d.mts dist/index-xnf.d.cts", "build": "npm run types && rollup -c", "rebuild": "npm run derive && npm run make && npm run validate && npm run test && npm run build", "order": "node validate/dump-group-order.js save && npm run rebuild"}, "devDependencies": {"@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-terser": "^0.4.4", "rollup": "^3.24.1"}, "type": "module"}