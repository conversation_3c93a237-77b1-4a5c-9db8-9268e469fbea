{"version": 3, "file": "WalletLinkSession.js", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/type/WalletLinkSession.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAEpD,MAAM,sBAAsB,GAAG,YAAY,CAAC;AAC5C,MAAM,0BAA0B,GAAG,gBAAgB,CAAC;AACpD,MAAM,0BAA0B,GAAG,gBAAgB,CAAC;AAEpD,MAAM,OAAO,iBAAiB;IAI5B,YACW,OAA2B,EAC3B,EAAU,EACV,MAAc,EACvB,MAAM,GAAG,KAAK;QAHL,YAAO,GAAP,OAAO,CAAoB;QAC3B,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAAQ;QAGvB,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,aAAa,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,OAA2B;QAC9C,MAAM,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,OAA2B;QAC5C,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAE3D,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC;YACjB,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,KAAK,GAAG,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAW,MAAM,CAAC,GAAY;QAC5B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7E,CAAC;CACF"}