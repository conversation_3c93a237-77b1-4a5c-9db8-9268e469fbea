{"version": 3, "file": "SCWSigner.js", "sourceRoot": "", "sources": ["../../../src/sign/scw/SCWSigner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAIvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAC1E,OAAO,EACL,cAAc,EACd,cAAc,EACd,oBAAoB,EACpB,sBAAsB,GACvB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,MAAM,YAAY,GAAG,UAAU,CAAC;AAChC,MAAM,wBAAwB,GAAG,aAAa,CAAC;AAC/C,MAAM,4BAA4B,GAAG,iBAAiB,CAAC;AACvD,MAAM,+BAA+B,GAAG,oBAAoB,CAAC;AAa7D,MAAM,OAAO,SAAS;IAUpB,YAAY,MAA0B;;QACpC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,EAAE,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAkB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAEnE,IAAI,CAAC,QAAQ,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,mCAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,wBAAwB,CAAC,IAAI;YAChE,EAAE,EAAE,MAAA,MAAA,MAAM,CAAC,QAAQ,CAAC,WAAW,0CAAG,CAAC,CAAC,mCAAI,CAAC;SAC1C,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAsB;;QACpC,0DAA0D;QAC1D,iFAAiF;QACjF,MAAM,CAAA,MAAA,MAAA,IAAI,CAAC,YAAY,EAAC,kBAAkB,kDAAI,CAAA,CAAC;QAE/C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;YACvD,SAAS,EAAE;gBACT,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAA,IAAI,CAAC,MAAM,mCAAI,EAAE,CAAC;aAC5D;SACF,CAAC,CAAC;QACH,MAAM,QAAQ,GACZ,MAAM,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;QAE1E,0BAA0B;QAC1B,IAAI,SAAS,IAAI,QAAQ,CAAC,OAAO;YAAE,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QAClE,MAAM,aAAa,GAAG,MAAM,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9E,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAEtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAChC,IAAI,OAAO,IAAI,MAAM;YAAE,MAAM,MAAM,CAAC,KAAK,CAAC;QAE1C,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,qBAAqB,CAAC,CAAC,CAAC;gBAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAwB,CAAC;gBACjD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACzB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACjD,MAAA,IAAI,CAAC,QAAQ,qDAAG,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBAC7C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAyB;;QACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,kBAAkB;oBACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAC1C;oBACE,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YACjD,CAAC;QACH,CAAC;QAED,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,qBAAqB;gBACxB,MAAA,IAAI,CAAC,QAAQ,qDAAG,SAAS,EAAE,EAAE,OAAO,EAAE,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC5E,OAAO,IAAI,CAAC,QAAQ,CAAC;YACvB,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,QAAQ,CAAC;YACvB,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,KAAK,aAAa;gBAChB,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC5C,KAAK,wBAAwB;gBAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;YAClE,KAAK,4BAA4B;gBAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAChD,KAAK,eAAe,CAAC;YACrB,KAAK,eAAe,CAAC;YACrB,KAAK,aAAa,CAAC;YACnB,KAAK,oBAAoB,CAAC;YAC1B,KAAK,qBAAqB,CAAC;YAC3B,KAAK,qBAAqB,CAAC;YAC3B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,sBAAsB,CAAC;YAC5B,KAAK,mBAAmB,CAAC;YACzB,KAAK,yBAAyB,CAAC;YAC/B,KAAK,mBAAmB,CAAC;YACzB,KAAK,kBAAkB,CAAC;YACxB,KAAK,wBAAwB,CAAC;YAC9B,KAAK,yBAAyB;gBAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC1C;gBACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;oBAAE,MAAM,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;gBACtF,OAAO,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAyB;;QACxD,0DAA0D;QAC1D,iFAAiF;QACjF,MAAM,CAAA,MAAA,MAAA,IAAI,CAAC,YAAY,EAAC,kBAAkB,kDAAI,CAAA,CAAC;QAE/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAChC,IAAI,OAAO,IAAI,MAAM;YAAE,MAAM,MAAM,CAAC,KAAK,CAAC;QAE1C,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,OAAO;;QACX,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG;YACX,EAAE,EAAE,MAAA,MAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,0CAAG,CAAC,CAAC,mCAAI,CAAC;SACxC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAyB;;QAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,MAItB,CAAC;QACF,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,OAAO,CAAA,EAAE,CAAC;YACnC,MAAM,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAC3C,CAAC;QACD,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEnD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,WAAW;YAAE,OAAO,IAAI,CAAC;QAE7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAyB;QAC1D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QAC7D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,CACxC,kEAAkE,CACnE,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,cAAc,CACpC;YACE,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;SACvB,EACD,YAAY,CACb,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,OAAqC;QAErC,MAAM,SAAS,GAAG,MAAM,oBAAoB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;QAChG,OAAO;YACL,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;YACvB,MAAM,EAAE,SAAS;YACjB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAA2B;;QAC9D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,6BAA6B;QAC7B,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,MAAM,OAAO,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QAC7D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,QAAQ,GAAgB,MAAM,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAEpF,MAAM,eAAe,GAAG,MAAA,QAAQ,CAAC,IAAI,0CAAE,MAAM,CAAC;QAC9C,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBACpE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;gBACd,MAAM;aACP,CAAC,CAAC,CAAC;YACJ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAA,QAAQ,CAAC,IAAI,0CAAE,YAAY,CAAC;QACvD,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,+BAA+B,EAAE,kBAAkB,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,WAAW,CAAC,OAAe,EAAE,kBAA4B;;QAC/D,MAAM,MAAM,GACV,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAU,4BAA4B,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAEzB,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAA,IAAI,CAAC,QAAQ,qDAAG,cAAc,EAAE,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF"}