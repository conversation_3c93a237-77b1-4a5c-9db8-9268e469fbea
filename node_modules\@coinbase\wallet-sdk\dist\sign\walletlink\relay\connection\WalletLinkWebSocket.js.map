{"version": 3, "file": "WalletLinkWebSocket.js", "sourceRoot": "", "sources": ["../../../../../src/sign/walletlink/relay/connection/WalletLinkWebSocket.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAIrE,MAAM,CAAN,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,qEAAY,CAAA;IACZ,iEAAU,CAAA;IACV,+DAAS,CAAA;AACX,CAAC,EAJW,eAAe,KAAf,eAAe,QAI1B;AAED,MAAM,OAAO,mBAAmB;IAM9B,0BAA0B,CAAC,QAAsC;QAC/D,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAGD,uBAAuB,CAAC,QAAoC;QAC1D,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,YACE,GAAW,EACM,iBAAmC,SAAS;QAA5C,mBAAc,GAAd,cAAc,CAA8B;QApBvD,cAAS,GAAqB,IAAI,CAAC;QACnC,gBAAW,GAAa,EAAE,CAAC;QAqBjC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;YAC3C,IAAI,SAAoB,CAAC;YACzB,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,MAAA,IAAI,CAAC,uBAAuB,qDAAG,eAAe,CAAC,UAAU,CAAC,CAAC;YAC3D,SAAS,CAAC,OAAO,GAAG,CAAC,GAAG,EAAE,EAAE;;gBAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAChE,MAAA,IAAI,CAAC,uBAAuB,qDAAG,eAAe,CAAC,YAAY,CAAC,CAAC;YAC/D,CAAC,CAAC;YACF,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;;gBACvB,OAAO,EAAE,CAAC;gBACV,MAAA,IAAI,CAAC,uBAAuB,qDAAG,eAAe,CAAC,SAAS,CAAC,CAAC;gBAE1D,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;oBACtC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC;YACF,SAAS,CAAC,SAAS,GAAG,CAAC,GAAG,EAAE,EAAE;;gBAC5B,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACrB,MAAA,IAAI,CAAC,oBAAoB,qDAAG;wBAC1B,IAAI,EAAE,WAAW;qBAClB,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAkB,CAAC;wBACtD,MAAA,IAAI,CAAC,oBAAoB,qDAAG,OAAO,CAAC,CAAC;oBACvC,CAAC;oBAAC,WAAM,CAAC;wBACP,WAAW;oBACb,CAAC;gBACH,CAAC;YACH,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,UAAU;;QACf,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAA,IAAI,CAAC,uBAAuB,qDAAG,eAAe,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;QACzC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAEtC,IAAI,CAAC;YACH,SAAS,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;QAAC,WAAM,CAAC;YACP,OAAO;QACT,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,IAAY;QAC1B,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5B,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEO,cAAc;QACpB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;IAC1B,CAAC;CACF"}