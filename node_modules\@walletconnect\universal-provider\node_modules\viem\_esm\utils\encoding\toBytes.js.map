{"version": 3, "file": "toBytes.js", "sourceRoot": "", "sources": ["../../../utils/encoding/toBytes.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAGhD,OAAO,EAAuB,KAAK,EAAE,MAAM,kBAAkB,CAAA;AAC7D,OAAO,EAAqB,GAAG,EAAE,MAAM,gBAAgB,CAAA;AAEvD,OAAO,EAA4B,UAAU,EAAE,MAAM,cAAc,CAAA;AACnE,OAAO,EAGL,WAAW,GACZ,MAAM,YAAY,CAAA;AAEnB,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,WAAW,EAAE,CAAA;AAe/C;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,OAAO,CACrB,KAA+C,EAC/C,OAA0B,EAAE;IAE5B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ;QACxD,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IACnC,IAAI,OAAO,KAAK,KAAK,SAAS;QAAE,OAAO,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IAC/D,IAAI,KAAK,CAAC,KAAK,CAAC;QAAE,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IAChD,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACnC,CAAC;AAYD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc,EAAE,OAAwB,EAAE;IACpE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;IAC/B,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;IACxB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAClC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACtC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IACxC,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,sEAAsE;AACtE,MAAM,WAAW,GAAG;IAClB,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,EAAE;IACR,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,GAAG;CACE,CAAA;AAEV,SAAS,gBAAgB,CAAC,IAAY;IACpC,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI;QACtD,OAAO,IAAI,GAAG,WAAW,CAAC,IAAI,CAAA;IAChC,IAAI,IAAI,IAAI,WAAW,CAAC,CAAC,IAAI,IAAI,IAAI,WAAW,CAAC,CAAC;QAChD,OAAO,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;IACpC,IAAI,IAAI,IAAI,WAAW,CAAC,CAAC,IAAI,IAAI,IAAI,WAAW,CAAC,CAAC;QAChD,OAAO,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;IACpC,OAAO,SAAS,CAAA;AAClB,CAAC;AASD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,UAAU,CAAC,IAAS,EAAE,OAAuB,EAAE;IAC7D,IAAI,GAAG,GAAG,IAAI,CAAA;IACd,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,UAAU,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACpC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAW,CAAA;IACtC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;QAAE,SAAS,GAAG,IAAI,SAAS,EAAE,CAAA;IAErD,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;IACnC,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA;IACpC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAC9D,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAC/D,IAAI,UAAU,KAAK,SAAS,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1D,MAAM,IAAI,SAAS,CACjB,2BAA2B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GACzC,SAAS,CAAC,CAAC,GAAG,CAAC,CACjB,SAAS,SAAS,KAAK,CACxB,CAAA;QACH,CAAC;QACD,KAAK,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,WAAW,CAAA;IAC9C,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAOD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,aAAa,CAC3B,KAAsB,EACtB,IAAkC;IAElC,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IACpC,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA;AACxB,CAAC;AAYD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,aAAa,CAC3B,KAAa,EACb,OAA0B,EAAE;IAE5B,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACnC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAClC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACtC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IACtD,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC"}