{"version": 3, "file": "toWebAuthnAccount.js", "sourceRoot": "", "sources": ["../../../account-abstraction/accounts/toWebAuthnAccount.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,SAAS,MAAM,cAAc,CAAA;AACzC,OAAO,KAAK,YAAY,MAAM,iBAAiB,CAAA;AAG/C,OAAO,EAAE,WAAW,EAAE,MAAM,sCAAsC,CAAA;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wCAAwC,CAAA;AA6BtE;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAC/B,UAAuC;IAEvC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,UAAU,CAAA;IAClC,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,UAAU,CAAA;IAC/C,OAAO;QACL,EAAE;QACF,SAAS;QACT,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE;YACjB,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC;gBAC3D,YAAY,EAAE,EAAE;gBAChB,KAAK;gBACL,SAAS,EAAE,IAAI;gBACf,IAAI;aACL,CAAC,CAAA;YACF,OAAO;gBACL,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;gBACrC,GAAG;gBACH,QAAQ,EAAE,QAAQ;aACnB,CAAA;QACH,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAClD,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,UAAU;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;QACvD,CAAC;QACD,IAAI,EAAE,UAAU;KACjB,CAAA;AACH,CAAC"}