{"version": 3, "file": "ScopedLocalStorage.js", "sourceRoot": "", "sources": ["../../../src/core/storage/ScopedLocalStorage.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,sDAAsD;AACtD,MAAM,OAAO,kBAAkB;IAC7B,YACU,KAA8B,EAC9B,MAAe;QADf,UAAK,GAAL,KAAK,CAAyB;QAC9B,WAAM,GAAN,MAAM,CAAS;IACtB,CAAC;IAEJ,WAAW,CAAI,GAAW,EAAE,IAAO;QACjC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,UAAU,CAAI,GAAW;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC7C,CAAC;IAEM,OAAO,CAAC,GAAW,EAAE,KAAa;QACvC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAEM,OAAO,CAAC,GAAW;QACxB,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IACnD,CAAC;IAEM,UAAU,CAAC,GAAW;QAC3B,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAClC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,SAAS,CAAC,GAAW;QACnB,OAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC;IACxE,CAAC;IAED,MAAM,CAAC,QAAQ;QACb,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,kBAAkB,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;IAC/C,CAAC;CACF"}