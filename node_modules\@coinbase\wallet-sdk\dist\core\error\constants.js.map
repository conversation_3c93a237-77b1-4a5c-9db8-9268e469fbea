{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../src/core/error/constants.ts"], "names": [], "mappings": "AAwBA,MAAM,CAAC,MAAM,kBAAkB,GAAe;IAC5C,GAAG,EAAE;QACH,YAAY,EAAE,CAAC,KAAK;QACpB,gBAAgB,EAAE,CAAC,KAAK;QACxB,mBAAmB,EAAE,CAAC,KAAK;QAC3B,mBAAmB,EAAE,CAAC,KAAK;QAC3B,kBAAkB,EAAE,CAAC,KAAK;QAC1B,aAAa,EAAE,CAAC,KAAK;QACrB,KAAK,EAAE,CAAC,KAAK;QACb,cAAc,EAAE,CAAC,KAAK;QACtB,cAAc,EAAE,CAAC,KAAK;QACtB,aAAa,EAAE,CAAC,KAAK;QACrB,QAAQ,EAAE,CAAC,KAAK;KACjB;IACD,QAAQ,EAAE;QACR,mBAAmB,EAAE,IAAI;QACzB,YAAY,EAAE,IAAI;QAClB,iBAAiB,EAAE,IAAI;QACvB,YAAY,EAAE,IAAI;QAClB,iBAAiB,EAAE,IAAI;QACvB,gBAAgB,EAAE,IAAI;KACvB;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,QAAQ,EAAE;QACR,QAAQ,EAAE,cAAc;QACxB,OAAO,EACL,uGAAuG;KAC1G;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,cAAc;QACxB,OAAO,EAAE,8CAA8C;KACxD;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,cAAc;QACxB,OAAO,EAAE,+CAA+C;KACzD;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,cAAc;QACxB,OAAO,EAAE,8BAA8B;KACxC;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,cAAc;QACxB,OAAO,EAAE,0BAA0B;KACpC;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,gBAAgB;KAC1B;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,qBAAqB;KAC/B;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,uBAAuB;KACjC;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,uBAAuB;KACjC;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,uBAAuB;KACjC;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,yBAAyB;KACnC;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,4BAA4B;KACtC;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,0EAA0E;KACpF;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,kEAAkE;KAC5E;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,+CAA+C;KACzD;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,wDAAwD;KAClE;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,wBAAwB;KAClC;CACF,CAAC"}